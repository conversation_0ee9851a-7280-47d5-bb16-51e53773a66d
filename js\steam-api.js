// Steam Web API Integration for SCUM Commands 2025
class SteamAPI {
    constructor() {
        this.baseURL = 'http://localhost:5001/api/steam'; // Our backend proxy
        this.fallbackURL = 'https://api.steampowered.com';
        this.scumAppId = '513710'; // SCUM Steam App ID
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
        
        // Rate limiting
        this.lastRequest = 0;
        this.minRequestInterval = 1000; // 1 second between requests
        
        // Check if backend is available
        this.backendAvailable = false;
        this.checkBackendHealth();
    }

    // Rate limiting helper
    async rateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequest;
        if (timeSinceLastRequest < this.minRequestInterval) {
            await new Promise(resolve => 
                setTimeout(resolve, this.minRequestInterval - timeSinceLastRequest)
            );
        }
        this.lastRequest = Date.now();
    }

    // Cache helper
    getCached(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCached(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    // Check backend health
    async checkBackendHealth() {
        try {
            const response = await fetch(`${this.baseURL}/health`, {
                method: 'GET',
                timeout: 5000
            });
            this.backendAvailable = response.ok;
            console.log(`Backend health check: ${this.backendAvailable ? 'Available' : 'Unavailable'}`);
        } catch (error) {
            console.warn('Backend not available, using fallback methods');
            this.backendAvailable = false;
        }
    }

    // Make API request with error handling
    async makeRequest(url) {
        await this.rateLimit();
        
        try {
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Steam API request failed:', error);
            throw error;
        }
    }

    // Get SCUM app details
    async getAppDetails() {
        const cacheKey = 'app_details';
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const url = `${this.baseURL}/ISteamApps/GetAppList/v2/`;
            const data = await this.makeRequest(url, true);
            
            const scumApp = data.applist.apps.find(app => app.appid.toString() === this.scumAppId);
            
            if (scumApp) {
                this.setCached(cacheKey, scumApp);
                return scumApp;
            }
            
            throw new Error('SCUM app not found');
        } catch (error) {
            console.error('Failed to get app details:', error);
            return null;
        }
    }

    // Get current player count
    async getCurrentPlayers() {
        const cacheKey = 'current_players';
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            if (this.backendAvailable) {
                const url = `${this.baseURL}/players`;
                const data = await this.makeRequest(url);
                
                if (data.player_count !== undefined) {
                    const playerCount = data.player_count;
                    this.setCached(cacheKey, playerCount);
                    return playerCount;
                }
            }
            
            // Fallback: return mock data if backend unavailable
            const mockCount = Math.floor(Math.random() * 5000) + 1000; // 1000-6000 players
            console.warn('Using mock player count data');
            this.setCached(cacheKey, mockCount);
            return mockCount;
        } catch (error) {
            console.error('Failed to get current players:', error);
            return null;
        }
    }

    // Get Steam store data (using Store API)
    async getStoreData() {
        const cacheKey = 'store_data';
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const url = `https://store.steampowered.com/api/appdetails?appids=${this.scumAppId}`;
            const data = await this.makeRequest(url, true);
            
            if (data[this.scumAppId] && data[this.scumAppId].success) {
                const storeData = data[this.scumAppId].data;
                this.setCached(cacheKey, storeData);
                return storeData;
            }
            
            throw new Error('Store data not available');
        } catch (error) {
            console.error('Failed to get store data:', error);
            return null;
        }
    }

    // Get game news
    async getGameNews(count = 5) {
        const cacheKey = `game_news_${count}`;
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            if (this.backendAvailable) {
                const url = `${this.baseURL}/news?count=${count}`;
                const data = await this.makeRequest(url);
                
                if (data.news && data.news.length > 0) {
                    const news = data.news;
                    this.setCached(cacheKey, news);
                    return news;
                }
            }
            
            // Fallback: return mock news data
            const mockNews = [
                {
                    title: "SCUM Update Available",
                    contents: "Latest SCUM update includes new items, vehicles, and gameplay improvements...",
                    url: "https://store.steampowered.com/news/app/513710",
                    date: Math.floor(Date.now() / 1000) - 86400 // 1 day ago
                },
                {
                    title: "Community Event",
                    contents: "Join the SCUM community for special events and competitions...",
                    url: "https://store.steampowered.com/news/app/513710",
                    date: Math.floor(Date.now() / 1000) - 172800 // 2 days ago
                }
            ];
            console.warn('Using mock news data');
            this.setCached(cacheKey, mockNews);
            return mockNews;
        } catch (error) {
            console.error('Failed to get game news:', error);
            return [];
        }
    }

    // Get achievement stats (global)
    async getGlobalAchievementStats() {
        const cacheKey = 'achievement_stats';
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const url = `${this.baseURL}/ISteamUserStats/GetGlobalAchievementPercentagesForApp/v2/?gameid=${this.scumAppId}`;
            const data = await this.makeRequest(url, true);
            
            if (data.achievementpercentages && data.achievementpercentages.achievements) {
                const achievements = data.achievementpercentages.achievements;
                this.setCached(cacheKey, achievements);
                return achievements;
            }
            
            throw new Error('No achievement data available');
        } catch (error) {
            console.error('Failed to get achievement stats:', error);
            return [];
        }
    }

    // Get Steam reviews summary
    async getReviewsSummary() {
        const cacheKey = 'reviews_summary';
        const cached = this.getCached(cacheKey);
        if (cached) return cached;

        try {
            const url = `https://store.steampowered.com/appreviews/${this.scumAppId}?json=1&num_per_page=0`;
            const data = await this.makeRequest(url, true);
            
            if (data.query_summary) {
                const summary = data.query_summary;
                this.setCached(cacheKey, summary);
                return summary;
            }
            
            throw new Error('No review data available');
        } catch (error) {
            console.error('Failed to get reviews summary:', error);
            return null;
        }
    }

    // Format player count with K/M suffixes
    formatPlayerCount(count) {
        if (count >= 1000000) {
            return (count / 1000000).toFixed(1) + 'M';
        } else if (count >= 1000) {
            return (count / 1000).toFixed(1) + 'K';
        }
        return count.toString();
    }

    // Format date from Unix timestamp
    formatDate(timestamp) {
        const date = new Date(timestamp * 1000);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Get comprehensive SCUM stats
    async getComprehensiveStats() {
        try {
            // Try backend comprehensive endpoint first
            if (this.backendAvailable) {
                try {
                    const url = `${this.baseURL}/comprehensive`;
                    const data = await this.makeRequest(url);
                    
                    if (data.data) {
                        return {
                            currentPlayers: data.data.players?.player_count || null,
                            storeData: data.data.store?.store_data || null,
                            news: data.data.news?.news || [],
                            reviews: data.data.reviews?.reviews || null,
                            lastUpdated: data.timestamp
                        };
                    }
                } catch (error) {
                    console.warn('Backend comprehensive endpoint failed, trying individual calls');
                }
            }

            // Fallback to individual calls
            const [currentPlayers, storeData, news, reviews] = await Promise.allSettled([
                this.getCurrentPlayers(),
                this.getStoreData(),
                this.getGameNews(3),
                this.getReviewsSummary()
            ]);

            return {
                currentPlayers: currentPlayers.status === 'fulfilled' ? currentPlayers.value : null,
                storeData: storeData.status === 'fulfilled' ? storeData.value : null,
                news: news.status === 'fulfilled' ? news.value : [],
                reviews: reviews.status === 'fulfilled' ? reviews.value : null,
                lastUpdated: new Date().toISOString()
            };
        } catch (error) {
            console.error('Failed to get comprehensive stats:', error);
            return null;
        }
    }
}

// Steam Server Query for real-time server data
class SteamServerQuery {
    constructor() {
        this.servers = [
            // Add popular SCUM servers here
            { name: "Official EU #1", ip: "**************", port: 7777 },
            { name: "Official US #1", ip: "*************", port: 7777 },
            { name: "Official AS #1", ip: "***************", port: 7777 }
        ];
    }

    // Note: Direct server queries require a backend service due to CORS
    // This is a placeholder for server status display
    async getServerStatus(serverIp, serverPort) {
        // In a real implementation, this would go through your backend
        // For now, return mock data
        return {
            name: "SCUM Server",
            players: Math.floor(Math.random() * 64),
            maxPlayers: 64,
            map: "Scum Island",
            ping: Math.floor(Math.random() * 100) + 20,
            online: Math.random() > 0.1 // 90% uptime simulation
        };
    }

    async getAllServersStatus() {
        const promises = this.servers.map(server => 
            this.getServerStatus(server.ip, server.port)
                .then(status => ({ ...server, ...status }))
                .catch(error => ({ ...server, online: false, error: error.message }))
        );

        return await Promise.all(promises);
    }
}

// UI Integration functions
class SteamUIIntegration {
    constructor() {
        this.steamAPI = new SteamAPI();
        this.serverQuery = new SteamServerQuery();
    }

    // Initialize Steam integration
    async initialize() {
        try {
            await this.loadPlayerCount();
            await this.loadGameNews();
            await this.loadServerStatus();
            await this.loadGameStats();
        } catch (error) {
            console.error('Failed to initialize Steam integration:', error);
        }
    }

    // Load and display current player count
    async loadPlayerCount() {
        try {
            const playerCount = await this.steamAPI.getCurrentPlayers();
            if (playerCount !== null) {
                this.updatePlayerCountDisplay(playerCount);
            }
        } catch (error) {
            console.error('Failed to load player count:', error);
        }
    }

    // Update player count in the UI
    updatePlayerCountDisplay(count) {
        const elements = document.querySelectorAll('.steam-player-count');
        elements.forEach(element => {
            element.textContent = this.steamAPI.formatPlayerCount(count);
            element.setAttribute('title', `${count.toLocaleString()} players currently online`);
        });

        // Add to stats section if it exists
        const statsContainer = document.querySelector('.hero-stats');
        if (statsContainer && !document.querySelector('.steam-stat-item')) {
            const statItem = document.createElement('div');
            statItem.className = 'stat-item steam-stat-item';
            statItem.innerHTML = `
                <div class="stat-number steam-player-count">${this.steamAPI.formatPlayerCount(count)}</div>
                <div class="stat-label">Online Now</div>
            `;
            statsContainer.appendChild(statItem);
        }
    }

    // Load and display game news
    async loadGameNews() {
        try {
            const news = await this.steamAPI.getGameNews(3);
            if (news.length > 0) {
                this.displayGameNews(news);
            }
        } catch (error) {
            console.error('Failed to load game news:', error);
        }
    }

    // Display game news in the UI
    displayGameNews(news) {
        const newsContainer = document.getElementById('steam-news');
        if (!newsContainer) return;

        const newsHTML = news.map(item => `
            <div class="news-item">
                <div class="news-header">
                    <h4 class="news-title">${item.title}</h4>
                    <span class="news-date">${this.steamAPI.formatDate(item.date)}</span>
                </div>
                <p class="news-content">${item.contents.substring(0, 150)}...</p>
                <a href="${item.url}" target="_blank" class="news-link">Read More</a>
            </div>
        `).join('');

        newsContainer.innerHTML = newsHTML;
    }

    // Load server status
    async loadServerStatus() {
        try {
            const servers = await this.serverQuery.getAllServersStatus();
            this.displayServerStatus(servers);
        } catch (error) {
            console.error('Failed to load server status:', error);
        }
    }

    // Display server status
    displayServerStatus(servers) {
        const serverContainer = document.getElementById('server-status');
        if (!serverContainer) return;

        const serversHTML = servers.map(server => `
            <div class="server-item ${server.online ? 'online' : 'offline'}">
                <div class="server-name">${server.name}</div>
                <div class="server-info">
                    ${server.online ? 
                        `<span class="server-players">${server.players}/${server.maxPlayers}</span>
                         <span class="server-ping">${server.ping}ms</span>` :
                        '<span class="server-offline">Offline</span>'
                    }
                </div>
            </div>
        `).join('');

        serverContainer.innerHTML = serversHTML;
    }

    // Load comprehensive game stats
    async loadGameStats() {
        try {
            const stats = await this.steamAPI.getComprehensiveStats();
            if (stats) {
                this.displayGameStats(stats);
            }
        } catch (error) {
            console.error('Failed to load game stats:', error);
        }
    }

    // Display comprehensive game stats
    displayGameStats(stats) {
        // Update review score if available
        if (stats.reviews && stats.reviews.review_score_desc) {
            const reviewElements = document.querySelectorAll('.steam-review-score');
            reviewElements.forEach(element => {
                element.textContent = stats.reviews.review_score_desc;
                element.setAttribute('title', 
                    `${stats.reviews.total_positive} positive out of ${stats.reviews.total_reviews} reviews`
                );
            });
        }

        // Update price if available
        if (stats.storeData && stats.storeData.price_overview) {
            const priceElements = document.querySelectorAll('.steam-price');
            priceElements.forEach(element => {
                const price = stats.storeData.price_overview.final_formatted || 'N/A';
                element.textContent = price;
            });
        }
    }

    // Create Steam info widget
    createSteamWidget() {
        const widget = document.createElement('div');
        widget.className = 'steam-widget';
        widget.innerHTML = `
            <h3 class="widget-title">
                <i class="fab fa-steam"></i>
                Steam Stats
            </h3>
            <div class="widget-content">
                <div class="steam-stat">
                    <span class="stat-label">Players Online:</span>
                    <span class="stat-value steam-player-count">Loading...</span>
                </div>
                <div class="steam-stat">
                    <span class="stat-label">User Reviews:</span>
                    <span class="stat-value steam-review-score">Loading...</span>
                </div>
                <div class="steam-stat">
                    <span class="stat-label">Price:</span>
                    <span class="stat-value steam-price">Loading...</span>
                </div>
            </div>
            <div id="steam-news" class="steam-news"></div>
            <div id="server-status" class="server-status"></div>
        `;

        return widget;
    }
}

// Export for use in main.js
window.SteamAPI = SteamAPI;
window.SteamUIIntegration = SteamUIIntegration; 