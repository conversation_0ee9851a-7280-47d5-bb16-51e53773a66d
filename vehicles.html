<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCUM Vehicle Codes List | SCUM Commands</title>
    <meta name="description" content="A searchable list of all vehicles, with their vehicle IDs (spawn codes) from SCUM on PC / Mac (Steam).">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/steam-integration.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-gamepad me-2"></i>SCUM Commands
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="commands.html">
                            <i class="fas fa-terminal me-1"></i>Commands
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="items.html">
                            <i class="fas fa-cube me-1"></i>Items
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="vehicles.html">
                            <i class="fas fa-car me-1"></i>Vehicles
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="npcs.html">
                            <i class="fas fa-users me-1"></i>NPCs
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light">
        <div class="container">
            <ol class="breadcrumb mb-0 py-3">
                <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                <li class="breadcrumb-item active">Vehicles</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header Section -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-body">
                        <h1 class="card-title h2 mb-3">
                            <i class="fas fa-car text-primary me-2"></i>
                            SCUM Vehicle Spawn Codes
                        </h1>
                        <hr>
                        <p class="mb-3">
                            Find below a searchable list of all vehicle IDs, along with their spawn commands, for cars in SCUM on Steam (PC / Mac).
                        </p>
                        <p class="mb-3">
                            To spawn vehicles in SCUM, the <strong>#SpawnVehicle</strong> command is used. To use this command, 
                            simply replace <span class="badge bg-dark">[Vehicle Code]</span> in 
                            <span class="badge bg-dark">#SpawnVehicle [Vehicle Code]</span> with the code of the vehicle you wish to spawn from the below list.
                        </p>
                        <p class="mb-3">
                            For example, <span class="badge bg-dark">#SpawnVehicle BP_SUV_01_A</span> would spawn a Blue SUV.
                        </p>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="commands.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-terminal me-1"></i>SCUM Admin Commands
                            </a>
                            <a href="items.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-cube me-1"></i>SCUM Item IDs
                            </a>
                            <a href="npcs.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-users me-1"></i>SCUM NPC IDs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="viewToggle" id="tableView" checked>
                                    <label class="btn btn-outline-primary" for="tableView">
                                        <i class="fas fa-table me-1"></i>Table View
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="viewToggle" id="cardView">
                                    <label class="btn btn-outline-primary" for="cardView">
                                        <i class="fas fa-th-large me-1"></i>Card View
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search vehicles...">
                                </div>
                            </div>
                        </div>

                        <!-- Type Filter -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-2">
                                    <button class="btn btn-sm btn-outline-secondary filter-btn active" data-type="all">
                                        All Types
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-type="SUV">
                                        SUV
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Count -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted" id="resultsCount">Loading vehicles...</span>
                    <div class="steam-integration">
                        <span class="steam-status" id="steamStatus">
                            <i class="fab fa-steam text-muted"></i>
                            <span class="ms-1">Steam Integration Ready</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vehicles Container -->
        <div class="row">
            <div class="col-12">
                <!-- Table View -->
                <div id="tableViewContainer" class="view-container">
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Name</th>
                                            <th>Code</th>
                                            <th>Type</th>
                                            <th>Spawn Command</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="vehiclesTableBody">
                                        <!-- Vehicles will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card View -->
                <div id="cardViewContainer" class="view-container d-none">
                    <div id="vehiclesCardContainer" class="row">
                        <!-- Vehicle cards will be loaded here -->
                    </div>
                </div>

                <!-- No Results -->
                <div id="noResults" class="text-center py-5 d-none">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No vehicles found</h4>
                    <p class="text-muted">Try adjusting your search terms or type filter.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>SCUM Commands Database</h6>
                    <p class="mb-0 text-muted">Comprehensive database for SCUM game commands, items, vehicles, and NPCs.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Not affiliated with Gamepires or Devolver Digital.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Copy Success Toast -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="copyToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                Spawn command copied to clipboard!
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/steam-api.js"></script>
    <script>
        // Vehicles page specific functionality
        let vehiclesData = [];
        let filteredVehicles = [];

        // Load vehicles data
        async function loadVehicles() {
            try {
                const response = await fetch('data/vehicles.json');
                vehiclesData = await response.json();
                filteredVehicles = [...vehiclesData];
                renderVehicles();
                updateResultsCount();
            } catch (error) {
                console.error('Error loading vehicles:', error);
                document.getElementById('resultsCount').textContent = 'Error loading vehicles';
            }
        }

        // Render vehicles in current view
        function renderVehicles() {
            const isTableView = document.getElementById('tableView').checked;
            
            if (isTableView) {
                renderTableView();
            } else {
                renderCardView();
            }
            
            updateNoResults();
        }

        // Render table view
        function renderTableView() {
            const tbody = document.getElementById('vehiclesTableBody');
            tbody.innerHTML = '';

            filteredVehicles.forEach(vehicle => {
                const row = document.createElement('tr');
                row.className = 'vehicle-row';
                
                row.innerHTML = `
                    <td>
                        <strong>${vehicle.name}</strong>
                    </td>
                    <td>
                        <code class="text-primary">${vehicle.code}</code>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${vehicle.type}</span>
                    </td>
                    <td>
                        <code class="text-muted">${vehicle.spawnCommand}</code>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm copy-btn" 
                                onclick="copyToClipboard('${vehicle.spawnCommand}')">
                            <i class="fas fa-copy me-1"></i>Copy
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // Render card view
        function renderCardView() {
            const container = document.getElementById('vehiclesCardContainer');
            container.innerHTML = '';

            filteredVehicles.forEach(vehicle => {
                const col = document.createElement('div');
                col.className = 'col-md-6 col-lg-4 mb-4';
                
                col.innerHTML = `
                    <div class="card h-100 vehicle-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">${vehicle.name}</h6>
                            <span class="badge bg-secondary">${vehicle.type}</span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>Vehicle Code:</strong><br>
                                <code class="text-primary">${vehicle.code}</code>
                            </div>
                            
                            <div class="mb-3">
                                <strong>Spawn Command:</strong><br>
                                <code class="text-muted">${vehicle.spawnCommand}</code>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm w-100 copy-btn" 
                                    onclick="copyToClipboard('${vehicle.spawnCommand}')">
                                <i class="fas fa-copy me-1"></i>Copy Spawn Command
                            </button>
                        </div>
                    </div>
                `;
                
                container.appendChild(col);
            });
        }

        // Filter vehicles
        function filterVehicles() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const activeType = document.querySelector('.filter-btn.active').dataset.type;
            
            filteredVehicles = vehiclesData.filter(vehicle => {
                const matchesSearch = !searchTerm || 
                    vehicle.name.toLowerCase().includes(searchTerm) ||
                    vehicle.code.toLowerCase().includes(searchTerm);
                
                const matchesType = activeType === 'all' || vehicle.type === activeType;
                
                return matchesSearch && matchesType;
            });
            
            renderVehicles();
            updateResultsCount();
        }

        // Update results count
        function updateResultsCount() {
            const count = filteredVehicles.length;
            const total = vehiclesData.length;
            document.getElementById('resultsCount').textContent = 
                `Showing ${count} of ${total} vehicles`;
        }

        // Update no results display
        function updateNoResults() {
            const noResults = document.getElementById('noResults');
            if (filteredVehicles.length === 0) {
                noResults.classList.remove('d-none');
                document.getElementById('tableViewContainer').classList.add('d-none');
                document.getElementById('cardViewContainer').classList.add('d-none');
            } else {
                noResults.classList.add('d-none');
                const isTableView = document.getElementById('tableView').checked;
                document.getElementById('tableViewContainer').classList.toggle('d-none', !isTableView);
                document.getElementById('cardViewContainer').classList.toggle('d-none', isTableView);
            }
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyToast();
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        // Show copy success toast
        function showCopyToast() {
            const toast = new bootstrap.Toast(document.getElementById('copyToast'));
            toast.show();
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            loadVehicles();
            
            // Search input
            document.getElementById('searchInput').addEventListener('input', filterVehicles);
            
            // View toggle
            document.querySelectorAll('input[name="viewToggle"]').forEach(radio => {
                radio.addEventListener('change', renderVehicles);
            });
            
            // Type filters
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    filterVehicles();
                });
            });
        });
    </script>
</body>
</html> 