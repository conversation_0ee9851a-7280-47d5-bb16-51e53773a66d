<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCUM Admin Commands List | SCUM Commands</title>
    <meta name="description" content="A searchable list of all admin commands from SCUM on Steam (PC / Mac). These commands include server and single player commands.">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/steam-integration.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.html">
                <i class="fas fa-gamepad me-2"></i>SCUM Commands
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="commands.html">
                            <i class="fas fa-terminal me-1"></i>Commands
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="items.html">
                            <i class="fas fa-cube me-1"></i>Items
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="vehicles.html">
                            <i class="fas fa-car me-1"></i>Vehicles
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="npcs.html">
                            <i class="fas fa-users me-1"></i>NPCs
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="bg-light">
        <div class="container">
            <ol class="breadcrumb mb-0 py-3">
                <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                <li class="breadcrumb-item active">Commands</li>
            </ol>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Header Section -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-body">
                        <h1 class="card-title h2 mb-3">
                            <i class="fas fa-terminal text-primary me-2"></i>
                            SCUM Admin Commands List
                        </h1>
                        <hr>
                        <p class="mb-3">
                            Find below a searchable list of <strong>all 19 console commands</strong> from the Steam (PC / Mac) game SCUM.
                        </p>
                        <p class="mb-3">
                            Commands in SCUM start with a hashtag (#) and are sent in the chat box. To open the chat, press your 
                            <span class="badge bg-dark">T</span> key. After typing a command (e.g. #ListPlayers), hit 
                            <span class="badge bg-dark">ENTER</span> to run it.
                        </p>
                        <p class="mb-3">
                            Type the name of a command into the search box to instantly search for a specific command. 
                            Use the "Table View" and "Card View" buttons to switch between views.
                        </p>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="items.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-cube me-1"></i>SCUM Item IDs
                            </a>
                            <a href="vehicles.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-car me-1"></i>SCUM Vehicle IDs
                            </a>
                            <a href="npcs.html" class="btn btn-primary btn-sm">
                                <i class="fas fa-users me-1"></i>SCUM NPC IDs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Controls -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="viewToggle" id="tableView" checked>
                                    <label class="btn btn-outline-primary" for="tableView">
                                        <i class="fas fa-table me-1"></i>Table View
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="viewToggle" id="cardView">
                                    <label class="btn btn-outline-primary" for="cardView">
                                        <i class="fas fa-th-large me-1"></i>Card View
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search commands...">
                                </div>
                            </div>
                        </div>

                        <!-- Category Filter -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex flex-wrap gap-2">
                                    <button class="btn btn-sm btn-outline-secondary filter-btn active" data-category="all">
                                        All Categories
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="General">
                                        General
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Player Management">
                                        Player Management
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Spawning">
                                        Spawning
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Teleportation">
                                        Teleportation
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Server Management">
                                        Server Management
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Environment">
                                        Environment
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary filter-btn" data-category="Debug">
                                        Debug
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Count -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted" id="resultsCount">Loading commands...</span>
                    <div class="steam-integration">
                        <span class="steam-status" id="steamStatus">
                            <i class="fab fa-steam text-muted"></i>
                            <span class="ms-1">Steam Integration Ready</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Commands Container -->
        <div class="row">
            <div class="col-12">
                <!-- Table View -->
                <div id="tableViewContainer" class="view-container">
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Command</th>
                                            <th>Description</th>
                                            <th>Syntax</th>
                                            <th>Category</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="commandsTableBody">
                                        <!-- Commands will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card View -->
                <div id="cardViewContainer" class="view-container d-none">
                    <div id="commandsCardContainer" class="row">
                        <!-- Command cards will be loaded here -->
                    </div>
                </div>

                <!-- No Results -->
                <div id="noResults" class="text-center py-5 d-none">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No commands found</h4>
                    <p class="text-muted">Try adjusting your search terms or category filter.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h6>SCUM Commands Database</h6>
                    <p class="mb-0 text-muted">Comprehensive database for SCUM game commands, items, vehicles, and NPCs.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        Not affiliated with Gamepires or Devolver Digital.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Copy Success Toast -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="copyToast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                Command copied to clipboard!
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/steam-api.js"></script>
    <script>
        // Commands page specific functionality
        let commandsData = [];
        let filteredCommands = [];

        // Load commands data
        async function loadCommands() {
            try {
                const response = await fetch('data/commands.json');
                commandsData = await response.json();
                filteredCommands = [...commandsData];
                renderCommands();
                updateResultsCount();
            } catch (error) {
                console.error('Error loading commands:', error);
                document.getElementById('resultsCount').textContent = 'Error loading commands';
            }
        }

        // Render commands in current view
        function renderCommands() {
            const isTableView = document.getElementById('tableView').checked;
            
            if (isTableView) {
                renderTableView();
            } else {
                renderCardView();
            }
            
            updateNoResults();
        }

        // Render table view
        function renderTableView() {
            const tbody = document.getElementById('commandsTableBody');
            tbody.innerHTML = '';

            filteredCommands.forEach(command => {
                const row = document.createElement('tr');
                row.className = 'command-row';
                
                const parametersText = command.parameters.length > 0 
                    ? command.parameters.map(p => `[${p.name}${p.required ? '' : '?'}]`).join(' ')
                    : 'None';

                row.innerHTML = `
                    <td>
                        <code class="text-primary">#${command.name}</code>
                    </td>
                    <td>${command.description}</td>
                    <td>
                        <code class="text-muted">${command.syntax}</code>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${command.category}</span>
                    </td>
                    <td>
                        <button class="btn btn-primary btn-sm copy-btn" 
                                onclick="copyToClipboard('${command.syntax}')">
                            <i class="fas fa-copy me-1"></i>Copy
                        </button>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // Render card view
        function renderCardView() {
            const container = document.getElementById('commandsCardContainer');
            container.innerHTML = '';

            filteredCommands.forEach(command => {
                const col = document.createElement('div');
                col.className = 'col-md-6 col-lg-4 mb-4';
                
                const parametersHtml = command.parameters.length > 0 
                    ? command.parameters.map(p => `
                        <div class="mb-2">
                            <strong>${p.name}${p.required ? '' : ' (Optional)'}:</strong>
                            <small class="text-muted d-block">${p.description}</small>
                        </div>
                    `).join('')
                    : '<p class="text-muted">No parameters required</p>';

                col.innerHTML = `
                    <div class="card h-100 command-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <code class="text-primary h6 mb-0">#${command.name}</code>
                            <span class="badge bg-secondary">${command.category}</span>
                        </div>
                        <div class="card-body">
                            <p class="card-text">${command.description}</p>
                            
                            <h6 class="mt-3">Syntax:</h6>
                            <code class="text-muted">${command.syntax}</code>
                            
                            ${command.parameters.length > 0 ? `
                                <h6 class="mt-3">Parameters:</h6>
                                ${parametersHtml}
                            ` : ''}
                            
                            <h6 class="mt-3">Example:</h6>
                            <code class="text-success">${command.example}</code>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm w-100 copy-btn" 
                                    onclick="copyToClipboard('${command.syntax}')">
                                <i class="fas fa-copy me-1"></i>Copy Command
                            </button>
                        </div>
                    </div>
                `;
                
                container.appendChild(col);
            });
        }

        // Filter commands
        function filterCommands() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const activeCategory = document.querySelector('.filter-btn.active').dataset.category;
            
            filteredCommands = commandsData.filter(command => {
                const matchesSearch = !searchTerm || 
                    command.name.toLowerCase().includes(searchTerm) ||
                    command.description.toLowerCase().includes(searchTerm) ||
                    command.syntax.toLowerCase().includes(searchTerm);
                
                const matchesCategory = activeCategory === 'all' || command.category === activeCategory;
                
                return matchesSearch && matchesCategory;
            });
            
            renderCommands();
            updateResultsCount();
        }

        // Update results count
        function updateResultsCount() {
            const count = filteredCommands.length;
            const total = commandsData.length;
            document.getElementById('resultsCount').textContent = 
                `Showing ${count} of ${total} commands`;
        }

        // Update no results display
        function updateNoResults() {
            const noResults = document.getElementById('noResults');
            if (filteredCommands.length === 0) {
                noResults.classList.remove('d-none');
                document.getElementById('tableViewContainer').classList.add('d-none');
                document.getElementById('cardViewContainer').classList.add('d-none');
            } else {
                noResults.classList.add('d-none');
                const isTableView = document.getElementById('tableView').checked;
                document.getElementById('tableViewContainer').classList.toggle('d-none', !isTableView);
                document.getElementById('cardViewContainer').classList.toggle('d-none', isTableView);
            }
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyToast();
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        // Show copy success toast
        function showCopyToast() {
            const toast = new bootstrap.Toast(document.getElementById('copyToast'));
            toast.show();
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            loadCommands();
            
            // Search input
            document.getElementById('searchInput').addEventListener('input', filterCommands);
            
            // View toggle
            document.querySelectorAll('input[name="viewToggle"]').forEach(radio => {
                radio.addEventListener('change', renderCommands);
            });
            
            // Category filters
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    filterCommands();
                });
            });
        });
    </script>
</body>
</html> 