/* Steam Integration Styles for SCUM Commands 2025 */

/* Steam Section */
.steam-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, rgba(23, 26, 43, 0.8), rgba(45, 55, 72, 0.8));
    border-radius: 20px;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.steam-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="steam-pattern" patternUnits="userSpaceOnUse" width="20" height="20"><path d="M10 0L20 10L10 20L0 10Z" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23steam-pattern)"/></svg>');
    pointer-events: none;
}

.steam-container {
    position: relative;
    z-index: 1;
}

/* Steam Widget */
.steam-widget {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.steam-widget:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(94, 129, 172, 0.5);
}

.steam-widget .widget-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.steam-widget .widget-title i {
    color: #5e81ac;
    font-size: 1.75rem;
}

.widget-content {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Steam Stats */
.steam-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.2s ease;
}

.steam-stat:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(94, 129, 172, 0.3);
}

.steam-stat .stat-label {
    font-size: 0.9rem;
    color: #94a3b8;
    font-weight: 500;
}

.steam-stat .stat-value {
    font-size: 1rem;
    color: #e2e8f0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.steam-player-count {
    color: #88c999 !important;
    font-weight: 700;
}

.steam-review-score {
    color: #fbbf24 !important;
}

.steam-price {
    color: #60a5fa !important;
}

/* Loading state */
.steam-stat .stat-value:contains("Loading") {
    color: #64748b;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Steam News */
.steam-news {
    margin-top: 1.5rem;
}

.news-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.news-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(94, 129, 172, 0.3);
    transform: translateX(4px);
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    gap: 1rem;
}

.news-title {
    font-size: 1rem;
    font-weight: 600;
    color: #e2e8f0;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.news-date {
    font-size: 0.8rem;
    color: #94a3b8;
    white-space: nowrap;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.news-content {
    font-size: 0.9rem;
    color: #cbd5e1;
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.news-link {
    color: #5e81ac;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.news-link:hover {
    color: #81a1c1;
    text-decoration: underline;
}

/* Server Status */
.server-status {
    margin-top: 1.5rem;
}

.server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.server-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: scale(1.02);
}

.server-item.online {
    border-left: 4px solid #88c999;
}

.server-item.offline {
    border-left: 4px solid #f87171;
    opacity: 0.7;
}

.server-name {
    font-weight: 600;
    color: #e2e8f0;
    font-size: 0.95rem;
}

.server-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.server-players {
    background: rgba(136, 201, 153, 0.2);
    color: #88c999;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
}

.server-ping {
    background: rgba(94, 129, 172, 0.2);
    color: #5e81ac;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
}

.server-offline {
    color: #f87171;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Steam Stat Item (for hero stats) */
.steam-stat-item {
    background: linear-gradient(135deg, rgba(94, 129, 172, 0.1), rgba(136, 201, 153, 0.1));
    border: 1px solid rgba(94, 129, 172, 0.3);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.steam-stat-item::before {
    content: '🎮';
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    opacity: 0.3;
    font-size: 1.5rem;
}

.steam-stat-item .stat-number {
    color: #88c999;
}

.steam-stat-item .stat-label {
    color: #5e81ac;
}

/* Responsive Design */
@media (max-width: 768px) {
    .steam-section {
        padding: 2rem 0;
        margin: 1rem 0;
    }
    
    .steam-widget {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .steam-widget .widget-title {
        font-size: 1.25rem;
    }
    
    .widget-content {
        gap: 0.75rem;
    }
    
    .steam-stat {
        padding: 0.5rem 0.75rem;
    }
    
    .news-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .news-date {
        align-self: flex-end;
    }
    
    .server-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .server-info {
        gap: 0.5rem;
        flex-wrap: wrap;
    }
}

/* Dark theme adjustments */
.dark-theme .steam-widget {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .steam-section {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9), rgba(30, 41, 59, 0.9));
}

.dark-theme .steam-stat,
.dark-theme .news-item,
.dark-theme .server-item {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.05);
}

/* Animation classes */
.steam-loading {
    position: relative;
}

.steam-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0.5rem;
    width: 12px;
    height: 12px;
    margin-top: -6px;
    border: 2px solid transparent;
    border-top: 2px solid #5e81ac;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.steam-success {
    border-left: 4px solid #88c999;
    background: rgba(136, 201, 153, 0.05);
}

.steam-error {
    border-left: 4px solid #f87171;
    background: rgba(248, 113, 113, 0.05);
}

.steam-warning {
    border-left: 4px solid #fbbf24;
    background: rgba(251, 191, 36, 0.05);
}

/* Tooltip styles for Steam elements */
.steam-tooltip {
    position: relative;
    cursor: help;
}

.steam-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.steam-tooltip:hover::after {
    opacity: 1;
} 