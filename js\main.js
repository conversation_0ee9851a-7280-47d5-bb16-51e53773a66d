// Main JavaScript for SCUM Commands 2025
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme
    initializeTheme();
    
    // Initialize navigation
    initializeNavigation();
    
    // Initialize global search
    initializeGlobalSearch();
    
    // Initialize animations
    initializeAnimations();
    
    // Initialize copy functionality
    initializeCopyButtons();
    
    // Initialize stats counter (for home page)
    initializeStatsCounter();
    
    // Initialize Steam integration
    initializeSteamIntegration();
});

// Theme Management
function initializeTheme() {
    const themeToggle = document.getElementById('themeToggle');
    const currentTheme = localStorage.getItem('theme') || 'light';
    
    // Set initial theme
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateThemeIcon(currentTheme);
    
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

function updateThemeIcon(theme) {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// Navigation Management
function initializeNavigation() {
    const navbarToggle = document.getElementById('navbarToggle');
    const navbarMenu = document.getElementById('navbarMenu');
    
    if (navbarToggle && navbarMenu) {
        navbarToggle.addEventListener('click', function() {
            navbarMenu.classList.toggle('active');
            navbarToggle.classList.toggle('active');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navbarToggle.contains(e.target) && !navbarMenu.contains(e.target)) {
                navbarMenu.classList.remove('active');
                navbarToggle.classList.remove('active');
            }
        });
    }
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    });
}

// Global Search
function initializeGlobalSearch() {
    const globalSearch = document.getElementById('globalSearch');
    
    if (globalSearch) {
        globalSearch.addEventListener('input', debounce(function(e) {
            const query = e.target.value.trim();
            if (query.length >= 2) {
                performGlobalSearch(query);
            } else {
                clearSearchSuggestions();
            }
        }, 300));
        
        globalSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = e.target.value.trim();
                if (query) {
                    redirectToSearch(query);
                }
            }
        });
    }
}

function performGlobalSearch(query) {
    // Simulate search suggestions
    const suggestions = [
        { type: 'item', name: 'AK-74 Assault Rifle', url: 'items.html#ak74' },
        { type: 'command', name: '#SpawnItem', url: 'commands.html#spawnitem' },
        { type: 'vehicle', name: 'M1025 Humvee', url: 'vehicles.html#humvee' },
        { type: 'npc', name: 'Military Puppet', url: 'npcs.html#military' }
    ].filter(item => item.name.toLowerCase().includes(query.toLowerCase()));
    
    displaySearchSuggestions(suggestions);
}

function displaySearchSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) return;
    
    if (suggestions.length === 0) {
        suggestionsContainer.style.display = 'none';
        return;
    }
    
    const suggestionsHTML = suggestions.map(suggestion => `
        <div class="suggestion-item" onclick="window.location.href='${suggestion.url}'">
            <div class="suggestion-type">${suggestion.type}</div>
            <div class="suggestion-name">${suggestion.name}</div>
        </div>
    `).join('');
    
    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

function clearSearchSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

function redirectToSearch(query) {
    // Determine the best page to redirect to based on query
    if (query.toLowerCase().includes('spawn') || query.toLowerCase().includes('#')) {
        window.location.href = `commands.html?search=${encodeURIComponent(query)}`;
    } else if (query.toLowerCase().includes('car') || query.toLowerCase().includes('bike') || 
               query.toLowerCase().includes('vehicle')) {
        window.location.href = `vehicles.html?search=${encodeURIComponent(query)}`;
    } else if (query.toLowerCase().includes('puppet') || query.toLowerCase().includes('animal') ||
               query.toLowerCase().includes('npc')) {
        window.location.href = `npcs.html?search=${encodeURIComponent(query)}`;
    } else {
        window.location.href = `items.html?search=${encodeURIComponent(query)}`;
    }
}

// Copy to Clipboard
function initializeCopyButtons() {
    // This will be called when copy buttons are clicked
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            showCopyToast();
        }).catch(function(err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showCopyToast();
        });
    };
}

function showCopyToast() {
    const toast = document.getElementById('copyToast');
    if (toast) {
        toast.classList.add('show');
        setTimeout(() => {
            toast.classList.remove('show');
        }, 2000);
    }
}

// Animations
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements that should animate in
    const animateElements = document.querySelectorAll('.card, .feature-item, .stat-item');
    animateElements.forEach(element => {
        observer.observe(element);
    });
}

// Stats Counter Animation (for homepage)
function initializeStatsCounter() {
    const counters = document.querySelectorAll('.stat-number');
    
    const observerOptions = {
        threshold: 0.7,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalCount = parseInt(target.getAttribute('data-count'));
                
                if (finalCount && !target.classList.contains('counted')) {
                    target.classList.add('counted');
                    animateCounter(target, finalCount);
                }
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => observer.observe(counter));
}

function animateCounter(element, target) {
    let current = 0;
    const increment = target / 100;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString();
    }, 20);
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Loading Management
function showLoading(container) {
    if (container) {
        container.classList.add('loading');
    }
}

function hideLoading(container) {
    if (container) {
        container.classList.remove('loading');
    }
}

// URL Parameters
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// Local Storage Management
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
    } catch (e) {
        console.warn('Failed to save to localStorage:', e);
    }
}

function loadFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.warn('Failed to load from localStorage:', e);
        return null;
    }
}

// Toast Notifications
function showToast(message, type = 'success', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Trigger animation
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Remove toast
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => document.body.removeChild(toast), 300);
    }, duration);
}

// Mobile Detection
function isMobile() {
    return window.innerWidth <= 768;
}

// Format Numbers
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Steam Integration Initialization
let steamUI = null;

function initializeSteamIntegration() {
    // Check if Steam API classes are available
    if (typeof SteamUIIntegration !== 'undefined') {
        steamUI = new SteamUIIntegration();
        
        // Initialize Steam features
        steamUI.initialize().then(() => {
            console.log('Steam integration initialized successfully');
            
            // Add Steam widget to sidebar if it exists
            addSteamWidgetToSidebar();
            
            // Refresh Steam data every 5 minutes
            setInterval(() => {
                refreshSteamData();
            }, 5 * 60 * 1000);
            
        }).catch(error => {
            console.warn('Steam integration failed to initialize:', error);
            // Show fallback content or hide Steam-dependent features
            hideSteamFeatures();
        });
    } else {
        console.warn('Steam API not available');
        hideSteamFeatures();
    }
}

function addSteamWidgetToSidebar() {
    if (!steamUI) return;
    
    // Look for a sidebar or create a Steam section
    let sidebar = document.querySelector('.sidebar');
    if (!sidebar) {
        // Create a Steam section in the main content area
        const steamSection = document.createElement('section');
        steamSection.className = 'steam-section';
        steamSection.innerHTML = `
            <div class="container">
                <h2 class="section-title">Live SCUM Stats</h2>
                <div class="steam-container" id="steam-container"></div>
            </div>
        `;
        
        // Insert after the quick access section
        const quickAccess = document.querySelector('.quick-access');
        if (quickAccess && quickAccess.nextSibling) {
            quickAccess.parentNode.insertBefore(steamSection, quickAccess.nextSibling);
        } else if (quickAccess) {
            quickAccess.parentNode.appendChild(steamSection);
        }
        
        sidebar = steamSection.querySelector('#steam-container');
    }
    
    if (sidebar) {
        const widget = steamUI.createSteamWidget();
        sidebar.appendChild(widget);
    }
}

function refreshSteamData() {
    if (steamUI) {
        console.log('Refreshing Steam data...');
        steamUI.loadPlayerCount();
        steamUI.loadGameStats();
        steamUI.loadServerStatus();
    }
}

function hideSteamFeatures() {
    // Hide or remove Steam-dependent UI elements
    const steamElements = document.querySelectorAll('.steam-widget, .steam-section, .steam-stat-item');
    steamElements.forEach(element => {
        element.style.display = 'none';
    });
}

// Enhanced stats counter with Steam data
function initializeStatsCounter() {
    const counters = document.querySelectorAll('.stat-number');
    
    const observerOptions = {
        threshold: 0.7,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalCount = parseInt(target.getAttribute('data-count'));
                
                if (finalCount && !target.classList.contains('counted')) {
                    target.classList.add('counted');
                    animateCounter(target, finalCount);
                }
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => observer.observe(counter));
}

// Add Steam-specific search functionality
function enhanceGlobalSearch() {
    const searchInput = document.getElementById('globalSearch');
    if (!searchInput) return;
    
    // Add Steam data to search suggestions
    searchInput.addEventListener('input', debounce(function(e) {
        const query = e.target.value.toLowerCase().trim();
        
        if (query.length >= 2) {
            const suggestions = performGlobalSearch(query);
            
            // Add Steam-related suggestions if relevant
            if (steamUI && (query.includes('steam') || query.includes('player') || query.includes('server'))) {
                suggestions.push({
                    title: 'Live Player Count',
                    description: 'View current SCUM players online',
                    category: 'steam',
                    action: () => scrollToSteamSection()
                });
                
                suggestions.push({
                    title: 'Server Status',
                    description: 'Check official server status',
                    category: 'steam',
                    action: () => scrollToServerStatus()
                });
            }
            
            displaySearchSuggestions(suggestions);
        } else {
            clearSearchSuggestions();
        }
    }, 300));
}

function scrollToSteamSection() {
    const steamSection = document.querySelector('.steam-section');
    if (steamSection) {
        steamSection.scrollIntoView({ behavior: 'smooth' });
        clearSearchSuggestions();
    }
}

function scrollToServerStatus() {
    const serverStatus = document.getElementById('server-status');
    if (serverStatus) {
        serverStatus.scrollIntoView({ behavior: 'smooth' });
        clearSearchSuggestions();
    }
}

// Enhanced toast notifications for Steam events
function showSteamNotification(message, type = 'info') {
    showToast(`🎮 ${message}`, type, 4000);
}

// Error handling for Steam API failures
function handleSteamError(error, context) {
    console.error(`Steam API Error (${context}):`, error);
    
    // Show user-friendly error message
    if (error.message.includes('CORS') || error.message.includes('fetch')) {
        showSteamNotification('Steam data temporarily unavailable', 'warning');
    } else if (error.message.includes('rate limit')) {
        showSteamNotification('Too many requests, please wait', 'warning');
    } else {
        showSteamNotification('Failed to load Steam data', 'error');
    }
}

// Add Steam data export functionality
function exportSteamData() {
    if (!steamUI || !steamUI.steamAPI) return;
    
    steamUI.steamAPI.getComprehensiveStats().then(stats => {
        if (stats) {
            const dataStr = JSON.stringify(stats, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `scum-steam-stats-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            showSteamNotification('Steam data exported successfully');
        }
    }).catch(error => {
        handleSteamError(error, 'export');
    });
}

// Add keyboard shortcuts for Steam features
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + S + S = Show Steam stats
    if ((e.ctrlKey || e.metaKey) && e.key === 's' && !e.shiftKey) {
        e.preventDefault();
        scrollToSteamSection();
    }
    
    // Ctrl/Cmd + S + R = Refresh Steam data
    if ((e.ctrlKey || e.metaKey) && e.key === 'r' && e.shiftKey) {
        e.preventDefault();
        refreshSteamData();
        showSteamNotification('Refreshing Steam data...');
    }
});

// Enhanced initialization for Steam integration
function initializePageSpecificFeatures() {
    const currentPage = window.location.pathname;
    
    // Initialize Steam features on specific pages
    if (currentPage.includes('index') || currentPage === '/' || currentPage === '') {
        // Home page - full Steam integration
        initializeSteamIntegration();
    } else if (currentPage.includes('servers') || currentPage.includes('tools')) {
        // Server/tools pages - server status only
        if (typeof SteamUIIntegration !== 'undefined') {
            steamUI = new SteamUIIntegration();
            steamUI.loadServerStatus();
        }
    }
}

// Call page-specific initialization
document.addEventListener('DOMContentLoaded', function() {
    initializePageSpecificFeatures();
});

// Export functions for global use
window.SCUM = {
    copyToClipboard,
    showToast,
    showLoading,
    hideLoading,
    getUrlParameter,
    saveToLocalStorage,
    loadFromLocalStorage,
    isMobile,
    formatNumber,
    debounce,
    throttle
}; 