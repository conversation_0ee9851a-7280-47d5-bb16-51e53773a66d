// Global Search JavaScript for SCUM Commands 2025

// Global search data cache
let searchCache = {
    items: [],
    commands: [],
    vehicles: [],
    npcs: [],
    lastUpdated: null
};

// Initialize global search
document.addEventListener('DOMContentLoaded', function() {
    initializeGlobalSearch();
    loadSearchData();
});

function initializeGlobalSearch() {
    const globalSearchInput = document.getElementById('globalSearch');
    const searchSuggestions = document.getElementById('searchSuggestions');
    
    if (globalSearchInput) {
        globalSearchInput.addEventListener('input', debounce(handleGlobalSearch, 300));
        globalSearchInput.addEventListener('focus', handleSearchFocus);
        globalSearchInput.addEventListener('keydown', handleSearchKeydown);
        
        // Close suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!globalSearchInput.contains(e.target) && !searchSuggestions?.contains(e.target)) {
                hideSuggestions();
            }
        });
    }
}

async function loadSearchData() {
    try {
        // Load all data for search
        const [itemsRes, commandsRes, vehiclesRes, npcsRes] = await Promise.all([
            fetch('/data/items.json').catch(() => ({ ok: false })),
            fetch('/data/commands.json').catch(() => ({ ok: false })),
            fetch('/data/vehicles.json').catch(() => ({ ok: false })),
            fetch('/data/npcs.json').catch(() => ({ ok: false }))
        ]);
        
        if (itemsRes.ok) {
            const itemsData = await itemsRes.json();
            searchCache.items = itemsData.items || itemsData;
        }
        
        if (commandsRes.ok) {
            searchCache.commands = await commandsRes.json();
        }
        
        if (vehiclesRes.ok) {
            const vehiclesData = await vehiclesRes.json();
            searchCache.vehicles = vehiclesData.vehicles || vehiclesData;
        }
        
        if (npcsRes.ok) {
            const npcsData = await npcsRes.json();
            searchCache.npcs = npcsData.npcs || npcsData;
        }
        
        searchCache.lastUpdated = Date.now();
        console.log('Search data loaded:', {
            items: searchCache.items.length,
            commands: searchCache.commands.length,
            vehicles: searchCache.vehicles.length,
            npcs: searchCache.npcs.length
        });
    } catch (error) {
        console.error('Error loading search data:', error);
    }
}

function handleGlobalSearch(event) {
    const query = event.target.value.trim();
    
    if (query.length < 2) {
        hideSuggestions();
        return;
    }
    
    const suggestions = performSearch(query);
    displaySuggestions(suggestions);
}

function handleSearchFocus(event) {
    const query = event.target.value.trim();
    if (query.length >= 2) {
        const suggestions = performSearch(query);
        displaySuggestions(suggestions);
    }
}

function handleSearchKeydown(event) {
    const suggestions = document.querySelectorAll('.suggestion-item');
    const activeSuggestion = document.querySelector('.suggestion-item.active');
    
    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault();
            if (activeSuggestion) {
                activeSuggestion.classList.remove('active');
                const next = activeSuggestion.nextElementSibling;
                if (next) {
                    next.classList.add('active');
                } else {
                    suggestions[0]?.classList.add('active');
                }
            } else {
                suggestions[0]?.classList.add('active');
            }
            break;
            
        case 'ArrowUp':
            event.preventDefault();
            if (activeSuggestion) {
                activeSuggestion.classList.remove('active');
                const prev = activeSuggestion.previousElementSibling;
                if (prev) {
                    prev.classList.add('active');
                } else {
                    suggestions[suggestions.length - 1]?.classList.add('active');
                }
            } else {
                suggestions[suggestions.length - 1]?.classList.add('active');
            }
            break;
            
        case 'Enter':
            event.preventDefault();
            if (activeSuggestion) {
                activeSuggestion.click();
            } else {
                const query = event.target.value.trim();
                if (query) {
                    redirectToSearch(query);
                }
            }
            break;
            
        case 'Escape':
            hideSuggestions();
            event.target.blur();
            break;
    }
}

function performSearch(query) {
    const lowerQuery = query.toLowerCase();
    const suggestions = [];
    const maxSuggestions = 8;
    
    // Search items
    const itemMatches = searchCache.items
        .filter(item => 
            item.name.toLowerCase().includes(lowerQuery) ||
            item.code.toLowerCase().includes(lowerQuery) ||
            (item.description && item.description.toLowerCase().includes(lowerQuery)) ||
            (item.tags && item.tags.some(tag => tag.toLowerCase().includes(lowerQuery)))
        )
        .slice(0, 3)
        .map(item => ({
            type: 'item',
            name: item.name,
            code: item.code,
            url: `items.html?search=${encodeURIComponent(query)}`,
            icon: 'fas fa-box',
            description: item.description?.substring(0, 50) + '...' || ''
        }));
    
    // Search commands
    const commandMatches = searchCache.commands
        .filter(command => 
            command.name.toLowerCase().includes(lowerQuery) ||
            command.description.toLowerCase().includes(lowerQuery) ||
            command.category.toLowerCase().includes(lowerQuery)
        )
        .slice(0, 2)
        .map(command => ({
            type: 'command',
            name: command.name,
            code: command.syntax,
            url: `commands.html?search=${encodeURIComponent(query)}`,
            icon: 'fas fa-terminal',
            description: command.description.substring(0, 50) + '...'
        }));
    
    // Search vehicles
    const vehicleMatches = searchCache.vehicles
        .filter(vehicle => 
            vehicle.name.toLowerCase().includes(lowerQuery) ||
            vehicle.code.toLowerCase().includes(lowerQuery) ||
            (vehicle.description && vehicle.description.toLowerCase().includes(lowerQuery))
        )
        .slice(0, 2)
        .map(vehicle => ({
            type: 'vehicle',
            name: vehicle.name,
            code: vehicle.code,
            url: `vehicles.html?search=${encodeURIComponent(query)}`,
            icon: 'fas fa-car',
            description: vehicle.description?.substring(0, 50) + '...' || ''
        }));
    
    // Search NPCs
    const npcMatches = searchCache.npcs
        .filter(npc => 
            npc.name.toLowerCase().includes(lowerQuery) ||
            npc.code.toLowerCase().includes(lowerQuery) ||
            (npc.description && npc.description.toLowerCase().includes(lowerQuery))
        )
        .slice(0, 1)
        .map(npc => ({
            type: 'npc',
            name: npc.name,
            code: npc.code,
            url: `npcs.html?search=${encodeURIComponent(query)}`,
            icon: 'fas fa-users',
            description: npc.description?.substring(0, 50) + '...' || ''
        }));
    
    // Combine and prioritize results
    suggestions.push(...itemMatches, ...commandMatches, ...vehicleMatches, ...npcMatches);
    
    return suggestions.slice(0, maxSuggestions);
}

function displaySuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) return;
    
    if (suggestions.length === 0) {
        hideSuggestions();
        return;
    }
    
    const suggestionsHTML = suggestions.map((suggestion, index) => `
        <div class="suggestion-item" onclick="navigateToSuggestion('${suggestion.url}')" data-index="${index}">
            <div class="suggestion-icon">
                <i class="${suggestion.icon}"></i>
            </div>
            <div class="suggestion-content">
                <div class="suggestion-header">
                    <span class="suggestion-type">${suggestion.type}</span>
                    <span class="suggestion-name">${suggestion.name}</span>
                </div>
                <div class="suggestion-code">${suggestion.code}</div>
                ${suggestion.description ? `<div class="suggestion-description">${suggestion.description}</div>` : ''}
            </div>
        </div>
    `).join('');
    
    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
    
    // Add hover listeners
    const suggestionItems = suggestionsContainer.querySelectorAll('.suggestion-item');
    suggestionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            suggestionItems.forEach(s => s.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

function hideSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
        suggestionsContainer.innerHTML = '';
    }
}

function navigateToSuggestion(url) {
    hideSuggestions();
    window.location.href = url;
}

function redirectToSearch(query) {
    hideSuggestions();
    
    // Determine the best page based on query content
    const lowerQuery = query.toLowerCase();
    
    if (lowerQuery.includes('spawn') || lowerQuery.includes('#') || lowerQuery.includes('command')) {
        window.location.href = `commands.html?search=${encodeURIComponent(query)}`;
    } else if (lowerQuery.includes('car') || lowerQuery.includes('bike') || lowerQuery.includes('vehicle') || 
               lowerQuery.includes('suv') || lowerQuery.includes('truck')) {
        window.location.href = `vehicles.html?search=${encodeURIComponent(query)}`;
    } else if (lowerQuery.includes('puppet') || lowerQuery.includes('animal') || lowerQuery.includes('npc') ||
               lowerQuery.includes('zombie') || lowerQuery.includes('bear') || lowerQuery.includes('wolf')) {
        window.location.href = `npcs.html?search=${encodeURIComponent(query)}`;
    } else {
        // Default to items page
        window.location.href = `items.html?search=${encodeURIComponent(query)}`;
    }
}

// Quick search functions for specific types
function searchItems(query) {
    return searchCache.items.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.code.toLowerCase().includes(query.toLowerCase())
    );
}

function searchCommands(query) {
    return searchCache.commands.filter(command => 
        command.name.toLowerCase().includes(query.toLowerCase()) ||
        command.description.toLowerCase().includes(query.toLowerCase())
    );
}

function searchVehicles(query) {
    return searchCache.vehicles.filter(vehicle => 
        vehicle.name.toLowerCase().includes(query.toLowerCase()) ||
        vehicle.code.toLowerCase().includes(query.toLowerCase())
    );
}

function searchNPCs(query) {
    return searchCache.npcs.filter(npc => 
        npc.name.toLowerCase().includes(query.toLowerCase()) ||
        npc.code.toLowerCase().includes(query.toLowerCase())
    );
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export functions for use in other scripts
window.searchFunctions = {
    searchItems,
    searchCommands,
    searchVehicles,
    searchNPCs,
    performSearch,
    loadSearchData
}; 