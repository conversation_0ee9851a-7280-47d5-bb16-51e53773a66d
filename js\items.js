// Items Page JavaScript for SCUM Commands 2025
let itemsData = [];
let filteredItems = [];
let currentView = 'table';
let currentPage = 1;
const itemsPerPage = 50;

// Initialize items page
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('items.html')) {
        initializeItemsPage();
    }
});

async function initializeItemsPage() {
    try {
        showLoading();
        await loadItemsData();
        initializeSearch();
        initializeFilters();
        initializeViewToggle();
        displayItems();
        hideLoading();
        
        // Check for URL search parameter
        const urlParams = new URLSearchParams(window.location.search);
        const searchQuery = urlParams.get('search');
        if (searchQuery) {
            document.getElementById('itemSearch').value = searchQuery;
            filterItems();
        }
    } catch (error) {
        console.error('Error initializing items page:', error);
        showError('Failed to load items data');
    }
}

async function loadItemsData() {
    try {
        const response = await fetch('/data/items.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        itemsData = data.items || data; // Handle both formats
        filteredItems = [...itemsData];
        console.log(`Loaded ${itemsData.length} items`);
    } catch (error) {
        console.error('Error loading items data:', error);
        // Fallback to empty array
        itemsData = [];
        filteredItems = [];
    }
}

function initializeSearch() {
    const searchInput = document.getElementById('searchInput') || document.getElementById('itemSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterItems, 300));
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterItems();
            }
        });
    }
}

function initializeFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const rarityFilter = document.getElementById('rarityFilter');
    const yearFilter = document.getElementById('yearFilter');
    
    if (categoryFilter) {
        populateCategoryFilter();
        categoryFilter.addEventListener('change', filterItems);
    }
    
    if (rarityFilter) {
        rarityFilter.addEventListener('change', filterItems);
    }
    
    if (yearFilter) {
        populateYearFilter();
        yearFilter.addEventListener('change', filterItems);
    }
}

function populateCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    if (!categoryFilter) return;
    
    const categories = [...new Set(itemsData.map(item => item.category))].sort();
    
    categoryFilter.innerHTML = '<option value="">All Categories</option>';
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
        categoryFilter.appendChild(option);
    });
}

function populateYearFilter() {
    const yearFilter = document.getElementById('yearFilter');
    if (!yearFilter) return;
    
    const years = [...new Set(itemsData.map(item => item.addedIn))].sort().reverse();
    
    yearFilter.innerHTML = '<option value="">All Years</option>';
    years.forEach(year => {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        yearFilter.appendChild(option);
    });
}

function initializeViewToggle() {
    const viewButtons = document.querySelectorAll('.view-btn');
    
    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const view = this.getAttribute('data-view');
            switchView(view);
            
            // Update active state
            viewButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Set initial view
    switchView('grid');
}

function switchView(view) {
    currentView = view;
    
    const itemsGrid = document.getElementById('itemsGrid');
    const itemsTable = document.getElementById('itemsTable');
    const itemsCompact = document.getElementById('itemsCompact');
    
    // Hide all views
    if (itemsGrid) itemsGrid.style.display = 'none';
    if (itemsTable) itemsTable.style.display = 'none';
    if (itemsCompact) itemsCompact.style.display = 'none';
    
    // Show selected view
    switch(view) {
        case 'table':
            if (itemsTable) itemsTable.style.display = 'block';
            break;
        case 'compact':
            if (itemsCompact) itemsCompact.style.display = 'block';
            break;
        case 'grid':
        default:
            if (itemsGrid) itemsGrid.style.display = 'grid';
            break;
    }
    
    displayItems();
}

function filterItems() {
    const searchInput = document.getElementById('searchInput') || document.getElementById('itemSearch');
    const searchTerm = searchInput?.value.toLowerCase() || '';
    const categoryFilter = document.getElementById('categoryFilter')?.value || '';
    const rarityFilter = document.getElementById('rarityFilter')?.value || '';
    const newItemsFilter = document.getElementById('newItemsFilter')?.value || '';
    
    filteredItems = itemsData.filter(item => {
        const matchesSearch = !searchTerm || 
            item.name.toLowerCase().includes(searchTerm) ||
            item.code.toLowerCase().includes(searchTerm) ||
            (item.description && item.description.toLowerCase().includes(searchTerm)) ||
            (item.tags && item.tags.some(tag => tag.toLowerCase().includes(searchTerm)));
        
        const matchesCategory = !categoryFilter || item.category === categoryFilter;
        const matchesRarity = !rarityFilter || item.rarity === rarityFilter;
        const matchesYear = !newItemsFilter || item.addedIn === newItemsFilter;
        
        return matchesSearch && matchesCategory && matchesRarity && matchesYear;
    });
    
    currentPage = 1;
    displayItems();
    updateItemsStats();
}

function displayItems() {
    if (filteredItems.length === 0) {
        showEmptyState();
        return;
    }
    
    if (currentView === 'table') {
        displayTableView();
    } else if (currentView === 'grid') {
        displayGridView();
    } else if (currentView === 'compact') {
        displayCompactView();
    }
    
    updateItemsStats();
}

function displayTableView() {
    const itemsTable = document.getElementById('itemsTable');
    const tableBody = document.getElementById('itemsTableBody');
    if (!itemsTable || !tableBody) return;
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = filteredItems.slice(startIndex, endIndex);
    
    const tableHTML = pageItems.map(item => `
        <tr>
            <td>
                <img src="${item.image || '/assets/items/default.png'}" 
                     alt="${item.name}" 
                     class="item-image"
                     style="width: 32px; height: 32px; border-radius: 4px;"
                     onerror="this.src='/assets/items/default.png'">
            </td>
            <td>
                <div class="item-name" style="font-weight: 600;">${item.name}</div>
                ${item.description ? `<div style="font-size: 0.75rem; color: var(--text-muted); margin-top: 2px;">${item.description.substring(0, 100)}${item.description.length > 100 ? '...' : ''}</div>` : ''}
            </td>
            <td>
                <span class="item-category" style="background: var(--primary-color); color: white; padding: 2px 8px; border-radius: 4px; font-size: 0.75rem;">${item.category}</span>
            </td>
            <td>
                <span class="item-rarity ${item.rarity}" style="padding: 2px 8px; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">${item.rarity}</span>
            </td>
            <td>
                <code style="background: var(--bg-secondary); padding: 2px 6px; border-radius: 4px; font-size: 0.875rem;">${item.code}</code>
            </td>
            <td>
                <button class="copy-btn" onclick="copySpawnCommand('${item.code}')" style="padding: 4px 8px; background: var(--accent-color); color: white; border: none; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-copy"></i>
                    Copy
                </button>
            </td>
        </tr>
    `).join('');
    
    tableBody.innerHTML = tableHTML;
    createPagination();
}

function displayGridView() {
    const itemsGrid = document.getElementById('itemsGrid');
    if (!itemsGrid) return;
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = filteredItems.slice(startIndex, endIndex);
    
    const cardsHTML = pageItems.map(item => `
        <div class="item-card" data-category="${item.category}" data-rarity="${item.rarity}" data-year="${item.addedIn}">
            <div class="item-image">
                <img src="${item.image || '/assets/items/default.png'}" 
                     alt="${item.name}" 
                     loading="lazy"
                     onerror="this.src='/assets/items/default.png'">
                <div class="item-rarity ${item.rarity}">${item.rarity}</div>
                ${item.addedIn === '2025' ? '<div class="item-new">NEW</div>' : ''}
            </div>
            <div class="item-content">
                <h3 class="item-name">${item.name}</h3>
                <p class="item-description">${item.description || 'No description available.'}</p>
                <div class="item-code">
                    <label>Item Code:</label>
                    <code class="code-value">${item.code}</code>
                    <button class="copy-btn" onclick="copyToClipboard('${item.code}')" title="Copy Code">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="item-spawn">
                    <label>Spawn Command:</label>
                    <code class="command-value">#SpawnItem ${item.code}</code>
                    <button class="copy-btn" onclick="copySpawnCommand('${item.code}')" title="Copy Command">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
                <div class="item-tags">
                    <span class="tag ${item.category}">${item.category}</span>
                    ${item.tags ? item.tags.map(tag => `<span class="tag">${tag}</span>`).join('') : ''}
                </div>
            </div>
        </div>
    `).join('');
    
    // Keep existing load more button
    const loadMoreContainer = itemsGrid.querySelector('.load-more-container');
    itemsGrid.innerHTML = cardsHTML;
    if (loadMoreContainer && endIndex < filteredItems.length) {
        itemsGrid.appendChild(loadMoreContainer);
    }
    
    createPagination();
}

function displayCompactView() {
    const itemsCompact = document.getElementById('itemsCompact');
    if (!itemsCompact) return;
    
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageItems = filteredItems.slice(startIndex, endIndex);
    
    const compactHTML = `
        <div class="items-compact-list">
            ${pageItems.map(item => `
                <div class="compact-item">
                    <img src="${item.image || '/assets/items/default.png'}" 
                         alt="${item.name}" 
                         class="compact-image"
                         onerror="this.src='/assets/items/default.png'">
                    <div class="compact-info">
                        <h4>${item.name}</h4>
                        <code>${item.code}</code>
                        <span class="compact-category">${item.category}</span>
                    </div>
                    <button class="copy-btn compact" onclick="copySpawnCommand('${item.code}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            `).join('')}
        </div>
    `;
    
    itemsCompact.innerHTML = compactHTML;
    createPagination();
}

function createPagination() {
    const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
    const paginationContainer = document.getElementById('paginationContainer');
    
    if (!paginationContainer) return;
    
    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }
    
    const pagination = new PaginationManager(paginationContainer, {
        currentPage: currentPage,
        totalPages: totalPages,
        maxVisible: 5,
        onPageChange: (page) => {
            currentPage = page;
            displayItems();
            
            // Scroll to top of items section
            const itemsSection = document.querySelector('.items-section');
            if (itemsSection) {
                itemsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }
    });
    
    pagination.render();
}



function updateItemsStats() {
    const resultsCount = document.getElementById('resultsCount');
    
    if (resultsCount) {
        const startIndex = (currentPage - 1) * itemsPerPage + 1;
        const endIndex = Math.min(currentPage * itemsPerPage, filteredItems.length);
        
        if (filteredItems.length === 0) {
            resultsCount.textContent = 'No items found';
        } else if (filteredItems.length === itemsData.length) {
            resultsCount.textContent = `Showing ${utils.formatNumber(filteredItems.length)}+ items`;
        } else {
            resultsCount.textContent = `Showing ${startIndex}-${endIndex} of ${filteredItems.length} items`;
        }
    }
}

function copySpawnCommand(itemCode) {
    const command = `#SpawnItem ${itemCode} 1`;
    
    navigator.clipboard.writeText(command).then(() => {
        showCopyToast('Spawn command copied to clipboard!');
    }).catch(err => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = command;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopyToast('Spawn command copied to clipboard!');
    });
}

function showCopyToast(message) {
    // Remove existing toast
    const existingToast = document.querySelector('.copy-toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create new toast
    const toast = document.createElement('div');
    toast.className = 'copy-toast';
    toast.innerHTML = `
        <i class="fas fa-check"></i>
        ${message}
    `;
    
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Hide toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

function showEmptyState() {
    const itemsGrid = document.getElementById('itemsGrid');
    const itemsTable = document.getElementById('itemsTable');
    const itemsCompact = document.getElementById('itemsCompact');
    
    const emptyHTML = `
        <div class="items-empty" style="text-align: center; padding: 3rem; color: var(--text-secondary);">
            <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <h3 style="margin-bottom: 0.5rem; color: var(--text-primary);">No items found</h3>
            <p>Try adjusting your search criteria or filters.</p>
            <button class="btn btn-primary" onclick="clearFilters()" style="margin-top: 1rem;">Clear Filters</button>
        </div>
    `;
    
    if (itemsGrid) itemsGrid.innerHTML = emptyHTML;
    if (itemsTable) {
        const tableBody = document.getElementById('itemsTableBody');
        if (tableBody) tableBody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 2rem;">No items found. Try adjusting your filters.</td></tr>';
    }
    if (itemsCompact) itemsCompact.innerHTML = emptyHTML;
}

function clearFilters() {
    const searchInput = document.getElementById('searchInput') || document.getElementById('itemSearch');
    const categoryFilter = document.getElementById('categoryFilter');
    const rarityFilter = document.getElementById('rarityFilter');
    const newItemsFilter = document.getElementById('newItemsFilter');
    
    if (searchInput) searchInput.value = '';
    if (categoryFilter) categoryFilter.value = '';
    if (rarityFilter) rarityFilter.value = '';
    if (newItemsFilter) newItemsFilter.value = '';
    
    filterItems();
}

function copyToClipboard(text) {
    ClipboardManager.copy(text, 'Item code copied to clipboard!');
}

function showLoading() {
    const itemsContainer = document.getElementById('itemsContainer');
    if (itemsContainer) {
        itemsContainer.innerHTML = `
            <div class="items-loading">
                <div class="spinner"></div>
                Loading items...
            </div>
        `;
    }
}

function hideLoading() {
    // Loading state will be replaced by displayItems()
}

function showError(message) {
    const itemsContainer = document.getElementById('itemsContainer');
    if (itemsContainer) {
        itemsContainer.innerHTML = `
            <div class="items-empty">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error</h3>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="location.reload()">Retry</button>
            </div>
        `;
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Make functions available globally
window.copySpawnCommand = copySpawnCommand;
window.clearFilters = clearFilters;
window.copyToClipboard = copyToClipboard; 