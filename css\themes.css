/* Themes CSS for SCUM Commands 2025 */

/* Theme Transitions */
* {
    transition: background-color var(--transition-normal), 
                color var(--transition-normal), 
                border-color var(--transition-normal);
}

/* Light Theme (Default) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-card: #ffffff;
    
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --text-light: #ecf0f1;
    
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #495057;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --bg-card: #2d2d2d;
    
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --text-light: #ffffff;
    
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.6);
}

/* Dark theme specific overrides */
[data-theme="dark"] .navbar {
    background-color: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .hero-background {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-theme="dark"] .card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .table {
    background-color: var(--bg-card);
}

[data-theme="dark"] .table th {
    background-color: var(--bg-tertiary);
}

[data-theme="dark"] .form-control {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
}

[data-theme="dark"] .btn-secondary {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .btn-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-dark);
}

[data-theme="dark"] .modal-content {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .toast {
    background-color: var(--bg-card);
    border-color: var(--border-color);
}

[data-theme="dark"] .search-suggestions {
    background-color: var(--bg-card);
    border-color: var(--border-color);
}

[data-theme="dark"] .suggestion-item:hover {
    background-color: var(--bg-tertiary);
}

/* Theme Toggle Animation */
.theme-toggle {
    position: relative;
    overflow: hidden;
}

.theme-toggle i {
    transition: transform var(--transition-normal);
}

.theme-toggle:hover i {
    transform: rotate(180deg);
}

/* Auto Theme Detection */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --bg-tertiary: #404040;
        --bg-card: #2d2d2d;
        
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
        --text-muted: #888888;
        --text-light: #ffffff;
        
        --border-color: #404040;
        --border-light: #333333;
        --border-dark: #555555;
        
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
        --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.6);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #ff4500;
        --primary-hover: #ff6000;
        --border-color: #000000;
        --text-primary: #000000;
        --text-secondary: #333333;
    }
    
    [data-theme="dark"] {
        --primary-color: #ff6b35;
        --primary-hover: #ff8a5b;
        --border-color: #ffffff;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
    
    .hero-background {
        animation: none !important;
    }
    
    .animate-fade-in-up {
        animation: none !important;
    }
    
    .animate-pulse {
        animation: none !important;
    }
}

/* Print Styles */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
    
    .navbar,
    .theme-toggle,
    .search-container,
    .btn,
    .modal,
    .toast {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .table {
        border: 1px solid black !important;
    }
    
    .table th,
    .table td {
        border: 1px solid black !important;
    }
} 