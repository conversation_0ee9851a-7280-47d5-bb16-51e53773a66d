'''Important Note:''' After [[Update 11-29-22|The Nuclear Update(0.8)]], the old style vehicles were disabled and removed from the game. This removal is '''TEMPORARY''' while the development team convert the old vehicles to the new modular system
'''Vehicles''' are a highly sought after commodity on SCUM Island. Not only will they allow the player to travel much faster, but they also provide an amount of protection from firearms and equipped to carry a large amount of gear for loot runs or raiding bases.
== Vehicle Options ==
Currently enabled vehicles:
* Land
** [[WolfsWagen]]
** [[Laika]]
** [[Ranger]]
** [[Dirt Bike]]
** [[Mountain Bike]]
** [[City Bicycle]]
** [[Metal Wheelbarrow]]
** [[Improvised wheelbarrow]]
** [[Quad]]
* Water
** [[Wooden Motorboat]]
** [[Small Improvised Raft]]
** [[Big Improvised Raft]]
** [[SUP]]
* Air
** [[Kinglet Duster]]
Currently disabled vehicles:
* Land
** [[SUV]]
** [[Tractor]]
** [[Sled]]
* Water
** [[Motorboat]]
* Air
** [[Kinglet Scout]]
** [[Kinglet Mariner (Seaplane)]]<!-- Commenting old information until rework brings vehicles back
There are five types of vehicles available if the sledge is included. Each vehicle has its own unique characteristics that will make one more suitable in a certain situations than others.

Top Speed:
: Pickup/SUV > Quad > Tractor
Acceleration:
: Quad > SUV > Pickup > Tractor
Offroad Traction:
: Quad > Tractor > Pickup > SUV
Health Points:
: Pickup/SUV/Tractor > Quad
The pickup truck and SUV are equipped with a handbrake ({{Key|Space}}) that allowed for tighter turns at speed, while the tractor and quad do not have this. <br>
The tractor and quad can never stall and will always start on the first attempt. <br>

{| class="wikitable" style="width: 300px; text-align: center; float: left;"
|+ '''# of occupants'''
! [[File:Pickup.png|28px|Pickup]] <br> Pickup !! [[File:SUV.png|28px|SUV]] <br> SUV !! [[File:Quad_A.png|28px|Quad]] <br> Quad !! [[File:Tractor.png|28px|Tractor]] <br> Tractor
|-
| 6 || 5 || 2 || 3
|}

{| style="float: left;"
|-
| <!-- Blank table for spacing of other two tables. I don't know another method. 
|}

{| class="wikitable" style="width: 300px; text-align: center; float: left;"
|+ '''Cargo space'''
! [[File:Pickup.png|28px|Pickup]] <br> Pickup !! [[File:SUV.png|28px|SUV]] <br> SUV !! [[File:Quad_A.png|28px|Quad]] <br> Quad !! [[File:Tractor.png|28px|Tractor]] <br> Tractor
|-
| 20x70 || 20x70 || 16x12 || 24x72
|}

kg?
-->

== Driving Skill ==
The [[Skills/Driving|driving skill]] affects:
* Top speed
* Braking distance
* Ignition success rate
* Turning responsiveness
* % chance of stalling when traveling under 10kmh
* % chance of stalling during a gear shift
* Gear shift duration. 
<!-- Commenting to retain information until vehicle rework brings items back
For the tractor it effects only the turning responsiveness. Turning responsiveness and top speed are also affected for the Quad.
{| class="wikitable" style="width: 300px; text-align: center;"
|+ '''Top speed (kmh) by skill level'''
! Level !![[File:Pickup.png|28px|Pickup]] <br> Pickup !! [[File:SUV.png|28px|SUV]] <br> SUV !! [[File:Quad_A.png|28px|Quad]] <br> Quad !! [[File:Tractor.png|28px|Tractor]] <br> Tractor
|-
| No Skill || 62 || 62 || 40 || 46
|-
| Basic || 93 || 93 || 60 || 46
|-
| Medium || 124 || 124 || 80 || 46
|-
| Advanced || 156 || 156 || 99 || 46
|}
-->
== Procurement ==
Vehicles will spawn around the island in a ruined and salvaged state. Some will be missing only a few parts, some a large amount, and some of them won't even have an engine. 
when you find a vehicle that you want to keep, your first step should most likely be to push the vehicle to a safe area or to the [[Mechanic|vehicle mechanic]].
Note: Multiple players can push the same vehicle to make the process faster
<gallery>
Modular Vehicles 02.jpg|Pushing broken vehicle
Modular Vehicles 03.jpg|Pushing broken vehicle with assistance
</gallery>

==Vehicle Spawning==
While vehicles can spawn at varying locations around the map, they will only spawn if a player is close to a potential spawn point and the conditions are met. There is a limit imposed by the server that determines the maximum number of vehicles (In all conditions)  that can be in one game.  The default limits are as follows:


Airplane Max Amount - 3*

Dirt Bike Max Amount - 20*

Dirt Bike Max Functional Amount - 20*

Dirt Bike Min Purchased Amount - 5*

Laika (Car) Max Functional Amount - 50*

Laika (Car) Max Functional Amount - 20*

Laika (Car) Min Purchased Amount - 5*

Motorboat Max Amount - 11*

Motorboat Max Functional Amount - 11*

Motorboat Min Purchased Amount - 5* 

Wheelbarrow Max Amount - 20*

Wheelbarrow Max Functional Amount - 10*

Wheelbarrow Min Purchased Amount - 10*

Wolfswagen (Car) Max Amount - 50*

Wolfswagen (Car) Max Functional Amount - 20*

Wolfswagen (Car) Min Purchased Amount - 5*

Bicycle Max Amount - 20*

Bicycle Max Functional Amount - 20*

Bicycle Min Purchased Amount - 5*


Both the vehicle and vehicle parts risk the possibility of despawning should you leave them on the ground (including inside a flagged area). A solution to this is to build large storage containers to hold the parts such as the [[Improvised Wardrobe|Improvised Wardrobe.]] Naming your vehicle via right clicking on it while in proximity will pause the despawn timer. 



By default a car that has not been driven, repaired or named runs the risk of despawning after 10 days. It is advisable to use your vehicle regularly to avoid the chance of it despawning. Removing all the parts from a vehicle leaving only the shell will still require 10 days to pass before the vehicle respawns.*


<nowiki>*</nowiki>These limits can vary from server to server.

== Repairing/Restoring your vehicle==
When you first find your new vehicle it is going to most likely be missing a good number of parts. All of the missing [[:Category:Vehicle Parts|parts]] can either be looted in the world or bought at the [[Mechanic|car mechanic]]. 
The engine can only be serviced/installed at the [[Mechanic]]
==[[:Category:Vehicle Modules|Vehicle Modules/Parts]] ==
There is a large list of different parts that can be added to your vehicle. You can see the full list of these parts [[:Category:Vehicle Modules|Here.]]
You can install/remove parts yourself if you have: 
*At least a medium level in the [[Skills/Engineering|Engineering Skill]]
*A [[Car Repair Kit]] (2 charges are used per item added/removed)
*A [[Car Jack]] if you are working on [[:Category:Wheels|Tires]], Doors, Hatch, Hood, Battery or Alternator (All [[:Category:Vehicle Armor|Vehicle Armor]] pieces can be removed without a [[Car Jack]], but can only be installed with one)
If you do not have the skill/kits required the [[Mechanic]] is willing to install/remove parts for you, at a cost. If you're using the [[Mechanic]] just make sure you park the car at the car lift station. You can then hold the interact key to lift it which gives you the ability to purchasing the parts by interacting with the part on your vehicle. Keep in mind that when your car is on the car lift, you will always use the mechanic's services. Use a car jack to install parts for free if you have medium [[Skills/Engineering|Engineering Skill]].
<gallery>
Modular Vehicles 07.jpg|Mechanic car part interface
Modular Vehicles 08.jpg|Using the car lift at the mechanic
</gallery>
===Essential/Basic Parts===
The essentials for making your car function are the following:
*At least 3 [[:Category:Wheels|Wheels]] (you will have issues with any less than 4 though)
*An [[Wolfs Wagen Engine|Engine]], which can only be obtained by getting the vehicle to a [[Mechanic]]
*At least the [[:Category:Seats|front left car seat]] (Drivers seat)
*A [[Car Battery]]
*An [[:Category:Engine Parts|Alternator]] (Not required, but the battery will not charge without one)
*Gas (can be transported via [[Gasoline Canister]])
Once your car is running, you can add additional protection such as [[:Category:Doors|doors]], [[:Category:Bumpers|bumpers]], trunks, and hoods.
===[[:Category:Vehicle Armor|Vehicle Armor]]===
Each of these modules also have [[:Category:Vehicle Armor|Armor]] that can be installed to assist in your protection. In order to install an armor upgrade, you need the base element as well as the upgrade element, which can be bought from the [[Mechanic|car mechanic]]. Adding armor decreases car handling, acceleration and top speed. Note: Wait with adding/Buying armor to your car if you are having issues controlling it. Leveling up Driving to at least Basic is recommended.
<gallery>
Modular Vehicles 07.jpg|Mechanic car part interface
Modular Vehicles 08.jpg|Using the car lift at the mechanic
</gallery>
==Salvaging Parts==
If you only need a few select parts from a vehicle, you can use a [[Car Repair Kit]] and a [[Car Jack]] to salvage the desired parts from other vehicle spawns (jack is only needed for tires). These will only ever be the basic unarmored versions of the parts, but are better than nothing for sure
Note: most vehicle parts are too large to fit in any inventory, so they must be carried by hand, punching or interacting with the environment will cause you to drop the part. Tires however are 8x8 cells and fit in a Hiking Backpack. Doors also fit in the Wolfsvagon's own inventory if you do not have the needed Engineering skill to install them right away.
<gallery>
Modular Vehicles 04.jpg|Using car jack to service vehicle
Modular Vehicles 05.jpg|Remove interface for taking parts while in service mode
</gallery><!--Disabling as new vehicles do not have vehicle combat enabled yet and do not currently take damage consistently due to bugs
== Vehicle Combat ==
[[File:Drive By Animation.gif]]

* Anyone within a vehicle can shoot from their seat
** The '''driver''' can shoot with '''handguns only''', as he still needs one hand to drive.
** The '''passengers''' are able to use '''rifled weapons''' to shoot.

== Health and Upkeep ==
If a vehicle is broken down, it can be pushed by the owner or any of their squadmates

=== Vehicle Damage ===
As a vehicles health lowers, damage will start to show on its body.

[[File:Vehicle Damage.jpg|600x600px]]

At 70%, 50%, 30%, and 0% each vehicle will show white smoke, black smoke, fire, and explode respectively. Once a vehicle is on fire it will continue to degrade in health until it blows up, destroying its contents, killing everyone inside, and damaging or killing players in the near vicinity.

As of [[Update 04-30-20]] if you don't have fuel or battery charges to start the car it will keep decaying. Car decay resets after a successful engine start


Vehicles can be repaired with a [[File:Car_Repair_Kit.png|18px|Car Repair Kit|link=Car_Repair_Kit]] [[Car Repair Kit]]. For this, a basic [[Skills/Engineering|engineering skill]] is required.

=== Tire damage ===
[[File:Flat Tires.png|600x600px]]

Tires are able to be shot, going flat when it happens.  

Flat tires make it easier to lose control and harder to accelerate and decelerate.

You will need a [[Tire Repair Kit]] or an [[Improvised Tire Repair Kit]] in order to fix the tires

=== Engine Damage ===
[[File:Check Engine Indicator.png|600x600px]]

Gun shots can directly damage the engine of a vehicle instead of the vehicle in its entirety.  

Engine damage makes your vehicle unable to start.

An engine block can be repaired with a [[File:Car_Repair_Kit.png|18px|Car Repair Kit|link=Car_Repair_Kit]] [[Car Repair Kit]].
-->
== Useful Tips== 
*Vehicle explosions will do damage to other vehicles and player built destructible structures.
*
==Change History==
{| class="wikitable"
|[[Update 11-29-22]]
|Added modular vehicles, added Wolfswagen, disabled non-modular vehicles
|-
|[[Update 06-02-22]]
|Owner and Squadmates are now the only players that can push a vehicle if it is claimed
|-
|[[Update 07-23-20]]
| Added "Drive By" shooting mechanics
Added tire damage
Added tire repair kits with improvised version
Added engine block damage
Admins can now remove locks from vehicles
|-
|[[Update 06-25-20]]
|Vehicle battery drain when starting engine, using horn, lights or radio is reduced by 50%
|-
|[[Update 05-20-20]]
|Added visual vehicle damage
|-
| style="width: 12%;" |[[Update 04-30-20]]
| style="width: 88%;" |Added fuel and battery mechanics
|-
|}
[[Category:Vehicles]]
[[tr:Araçlar]]
