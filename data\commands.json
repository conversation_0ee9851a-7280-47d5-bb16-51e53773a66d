[{"name": "#", "description": "List all available commands", "syntax": "#", "parameters": [], "example": "#", "category": "General"}, {"name": "ListPlayers", "description": "Display a list of all players currently connected to the server", "syntax": "#ListPlayers", "parameters": [], "example": "#ListPlayers", "category": "Player Management"}, {"name": "Kick", "description": "Kick a player from the server", "syntax": "#Kick [Player]", "parameters": [{"name": "Player", "description": "The Steam64 ID, Steam name, or character name, of the player you wish to kick from the server.", "type": "SteamID", "required": true}], "example": "#Kick Gaben123", "category": "Player Management"}, {"name": "Ban", "description": "Ban a player from the server", "syntax": "#Ban [Player]", "parameters": [{"name": "Player", "description": "The Steam64 ID, Steam name, or character name, of the player you wish to ban from the server.", "type": "SteamID", "required": true}], "example": "#Ban Gaben123", "category": "Player Management"}, {"name": "ListItems", "description": "List all available item codes, optionally filtered by search term", "syntax": "#ListItems [Search Term]", "parameters": [{"name": "Search Term", "description": "Optional. If you specify a term here, only IDs containing this text will be listed. (e.g. 'gauge' would return a list of assets containing the word gauge).", "type": "Text", "required": false}], "example": "#ListItems gauge", "category": "Spawning"}, {"name": "ListCharacters", "description": "List all available character/NPC codes, optionally filtered by search term", "syntax": "#ListCharacters [Search Term]", "parameters": [{"name": "Search Term", "description": "Optional. If you specify a term here, only IDs containing this text will be listed. (e.g. 'zombie' would return a list of assets containing the word zombie).", "type": "Text", "required": false}], "example": "#ListCharacters zombie", "category": "Spawning"}, {"name": "ListVehicles", "description": "List all available vehicle codes, optionally filtered by search term", "syntax": "#ListVehicles [Search Term]", "parameters": [{"name": "Search Term", "description": "Optional. If you specify a term here, only IDs containing this text will be listed. (e.g. 'suv' would return a list of assets containing the word suv).", "type": "Text", "required": false}], "example": "#ListVehicles suv", "category": "Spawning"}, {"name": "Location", "description": "Get the current location coordinates of a player", "syntax": "#Location [Player]", "parameters": [{"name": "Player", "description": "The Steam64 ID, Steam name, or character name, of the player you wish to find out the location of.", "type": "SteamID", "required": true}], "example": "#Location Gaben123", "category": "Player Management"}, {"name": "SpawnItem", "description": "Spawn an item in front of your character", "syntax": "#SpawnItem [Item Code] [Amount]", "parameters": [{"name": "Item Code", "description": "The item code (also known as an item ID) of the item you wish to spawn in front of your character.", "type": "Item Code", "required": true}, {"name": "Amount", "description": "Optional. The amount of the specified item you wish to spawn. If no number is specified, one of the specified item will be spawned.", "type": "Number", "required": false}], "example": "#SpawnItem BP_Rifle_M1Garand 1", "category": "Spawning"}, {"name": "SpawnCharacter", "description": "Spawn an NPC/character in front of your character", "syntax": "#SpawnCharacter [NPC Code] [Amount]", "parameters": [{"name": "NPC Code", "description": "The NPC code (also known as a character code or ID) of the character you wish to spawn in front of your own character.", "type": "NPC Code", "required": true}, {"name": "Amount", "description": "Optional. The amount of the specified NPC to spawn. If not specified, one NPC will be spawned.", "type": "Number", "required": false}], "example": "#SpawnCharacter BP_Bear 1", "category": "Spawning"}, {"name": "SpawnVehicle", "description": "Spawn a vehicle in front of your character", "syntax": "#SpawnVehicle [Vehicle Code]", "parameters": [{"name": "Vehicle Code", "description": "The vehicle code (also known as a vehicle ID) of the vehicle you wish to spawn in front of your own character.", "type": "Vehicle Code", "required": true}], "example": "#SpawnVehicle BP_SUV_01_A", "category": "Spawning"}, {"name": "Teleport", "description": "Teleport a player to specific coordinates or to your location", "syntax": "#Teleport [Player] [X] [Y] [Z]", "parameters": [{"name": "Player", "description": "The Steam64 ID, Steam name, or character name, of the player you wish to teleport.", "type": "Text", "required": true}, {"name": "X", "description": "Optional - if you do not specify coordinates, the specified player will be teleported to your own character. The X coordinate of the location you wish to teleport the player to.", "type": "Number", "required": false}, {"name": "Y", "description": "Optional - if you do not specify coordinates, the specified player will be teleported to your own character. The Y coordinate of the location you wish to teleport the player to.", "type": "Number", "required": false}, {"name": "Z", "description": "Optional - if you do not specify coordinates, the specified player will be teleported to your own character. The Z coordinate of the location you wish to teleport the player to.", "type": "Number", "required": false}], "example": "#Teleport Gaben123 1000 2000 100", "category": "Teleportation"}, {"name": "TeleportToPlayer", "description": "Teleport yourself to another player", "syntax": "#TeleportToPlayer [Player]", "parameters": [{"name": "Player", "description": "The Steam64 ID, Steam name, or character name, of the player you wish to teleport to.", "type": "Text", "required": true}], "example": "#TeleportToPlayer Gaben123", "category": "Teleportation"}, {"name": "SetFamePoints", "description": "Set the Fame Points of a player to a specific amount", "syntax": "#SetFamePoints [Amount] [Player]", "parameters": [{"name": "Amount", "description": "The amount to set the Fame Points to. This is is not added to the player's current Fame Points - their Fame Points will be set to this number.", "type": "Number", "required": true}, {"name": "Player", "description": "Optional: if not specified, your own Fame Points will be changed. The Steam64 ID, Steam name, or character name, of the player you wish to change the Fame Points of.", "type": "Text", "required": false}], "example": "#SetFamePoints 1000 Gaben123", "category": "Player Management"}, {"name": "SetFamePointsToAllOnline", "description": "Set the Fame Points of all online players to a specific amount", "syntax": "#SetFamePointsToAllOnline [Amount]", "parameters": [{"name": "Amount", "description": "The amount to set the Fame Points of all online players to.", "type": "Number", "required": true}], "example": "#SetFamePointsToAllOnline 1000", "category": "Server Management"}, {"name": "SetFamePointsToAll", "description": "Set the Fame Points of all players (online and offline) to a specific amount", "syntax": "#SetFamePointsToAll [Amount]", "parameters": [{"name": "Amount", "description": "The amount to set the Fame Points of all players who have ever connected to the server to (both online and offline).", "type": "Number", "required": true}], "example": "#SetFamePointsToAll 1000", "category": "Server Management"}, {"name": "SetTime", "description": "Set the time of day on the server", "syntax": "#SetTime [0-24]", "parameters": [{"name": "0 - 24", "description": "A number between 0 and 24 - the hour on a 24 hour clock to set the time to. 11 is 11AM, 23 is 11PM.", "type": "Number", "required": true}], "example": "#SetTime 12", "category": "Environment"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Set the weather conditions on the server", "syntax": "#<PERSON><PERSON><PERSON><PERSON> [0-1]", "parameters": [{"name": "0 - 1", "description": "A number between 0 and 1, 0 being sunny clear skies and 1 being the most severe form of weather. 0.5 is the rain threshold (0.5 and above is rainy weather).", "type": "Number", "required": true}], "example": "#SetWeather 0.5", "category": "Environment"}, {"name": "VisualizeBulletTrajectories", "description": "Enable or disable bullet trajectory visualization", "syntax": "#VisualizeBulletTrajectories [true/false]", "parameters": [{"name": "true / false", "description": "Set to 'true' (without quotes) to enable bullet trajectories. Set to 'false' (without quotes) to disable bullet trajectories (false is the default setting).", "type": "Boolean", "required": true}], "example": "#VisualizeBulletTrajectories true", "category": "Debug"}]