#!/usr/bin/env python3
"""
SCUM Commands 2025 - Local HTTP Server
A modern Python server for serving the SCUM commands database locally.
"""

import http.server
import socketserver
import json
import os
import sys
import webbrowser
from urllib.parse import urlparse, parse_qs
from datetime import datetime
import threading
import time

class <PERSON>UM<PERSON><PERSON><PERSON>equestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom HTTP request handler for SCUM Commands website"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        """Add CORS headers for API requests"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query_params = parse_qs(parsed_path.query)
        
        # API endpoints
        if path.startswith('/api/'):
            self.handle_api_request(path, query_params)
        else:
            # Serve static files
            super().do_GET()
    
    def handle_api_request(self, path, query_params):
        """Handle API requests"""
        try:
            if path == '/api/items':
                self.serve_items_api(query_params)
            elif path == '/api/vehicles':
                self.serve_vehicles_api(query_params)
            elif path == '/api/commands':
                self.serve_commands_api(query_params)
            elif path == '/api/npcs':
                self.serve_npcs_api(query_params)
            elif path == '/api/search':
                self.serve_search_api(query_params)
            elif path == '/api/stats':
                self.serve_stats_api()
            else:
                self.send_error(404, "API endpoint not found")
        except Exception as e:
            self.send_error(500, f"Internal server error: {str(e)}")
    
    def serve_items_api(self, query_params):
        """Serve items data with filtering and pagination"""
        try:
            with open('data/items.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            items = data['items']
            
            # Apply filters
            category = query_params.get('category', [None])[0]
            rarity = query_params.get('rarity', [None])[0]
            search = query_params.get('search', [None])[0]
            year = query_params.get('year', [None])[0]
            
            if category:
                items = [item for item in items if item['category'] == category]
            
            if rarity:
                items = [item for item in items if item['rarity'] == rarity]
            
            if year:
                items = [item for item in items if item.get('addedIn') == year]
            
            if search:
                search_lower = search.lower()
                items = [item for item in items if 
                        search_lower in item['name'].lower() or
                        search_lower in item['code'].lower() or
                        search_lower in item['description'].lower() or
                        any(search_lower in tag.lower() for tag in item.get('tags', []))]
            
            # Pagination
            page = int(query_params.get('page', [1])[0])
            per_page = int(query_params.get('per_page', [50])[0])
            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            
            total_items = len(items)
            paginated_items = items[start_idx:end_idx]
            
            response = {
                'items': paginated_items,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_items,
                    'pages': (total_items + per_page - 1) // per_page
                },
                'categories': data.get('categories', {}),
                'rarity': data.get('rarity', {})
            }
            
            self.send_json_response(response)
            
        except FileNotFoundError:
            # Fallback to example data if file doesn't exist
            self.serve_example_items()
    
    def serve_example_items(self):
        """Serve example items data when JSON file is not available"""
        example_items = {
            'items': [
                {
                    'id': 'ak74_rifle',
                    'name': 'AK-74 Assault Rifle',
                    'code': 'AK74_Rifle',
                    'description': 'High-damage assault rifle with excellent range and reliability.',
                    'category': 'weapons',
                    'rarity': 'rare',
                    'addedIn': '2025',
                    'tags': ['automatic', 'rifle', 'military']
                },
                {
                    'id': 'tactical_vest',
                    'name': 'Tactical Vest',
                    'code': 'Tactical_Vest_01',
                    'description': 'Lightweight body armor providing moderate protection.',
                    'category': 'armor',
                    'rarity': 'uncommon',
                    'addedIn': '2024',
                    'tags': ['protection', 'armor', 'vest']
                }
            ],
            'pagination': {'page': 1, 'per_page': 50, 'total': 2, 'pages': 1}
        }
        self.send_json_response(example_items)
    
    def serve_vehicles_api(self, query_params):
        """Serve vehicles data"""
        try:
            with open('data/vehicles.json', 'r', encoding='utf-8') as f:
                vehicles_data = json.load(f)
            
            # Apply filters
            vehicle_type = query_params.get('type', [None])[0]
            search = query_params.get('search', [None])[0]
            
            vehicles = vehicles_data
            
            if vehicle_type:
                vehicles = [v for v in vehicles if v['type'] == vehicle_type]
            
            if search:
                search_lower = search.lower()
                vehicles = [v for v in vehicles if 
                          search_lower in v['name'].lower() or
                          search_lower in v['code'].lower()]
            
            response = {'vehicles': vehicles}
            self.send_json_response(response)
            
        except FileNotFoundError:
            # Fallback to example data if file doesn't exist
            vehicles = {
                'vehicles': [
                    {
                        'name': 'Blue SUV',
                        'code': 'BP_SUV_01_A',
                        'spawnCommand': '#SpawnVehicle BP_SUV_01_A',
                        'category': 'Vehicles',
                        'type': 'SUV'
                    }
                ]
            }
            self.send_json_response(vehicles)
    
    def serve_commands_api(self, query_params):
        """Serve commands data"""
        try:
            with open('data/commands.json', 'r', encoding='utf-8') as f:
                commands_data = json.load(f)
            
            # Apply filters
            category = query_params.get('category', [None])[0]
            search = query_params.get('search', [None])[0]
            
            commands = commands_data
            
            if category:
                commands = [cmd for cmd in commands if cmd['category'] == category]
            
            if search:
                search_lower = search.lower()
                commands = [cmd for cmd in commands if 
                          search_lower in cmd['name'].lower() or
                          search_lower in cmd['description'].lower() or
                          search_lower in cmd['syntax'].lower()]
            
            response = {'commands': commands}
            self.send_json_response(response)
            
        except FileNotFoundError:
            # Fallback to example data if file doesn't exist
            commands = {
                'commands': [
                    {
                        'name': 'SpawnItem',
                        'syntax': '#SpawnItem <ItemCode> [Quantity]',
                        'description': 'Spawns the specified item in your inventory.',
                        'category': 'Spawning',
                        'example': '#SpawnItem AK74_Rifle'
                    }
                ]
            }
            self.send_json_response(commands)
    
    def serve_npcs_api(self, query_params):
        """Serve NPCs data"""
        try:
            with open('data/npcs.json', 'r', encoding='utf-8') as f:
                npcs_data = json.load(f)
            
            # Apply filters
            category = query_params.get('category', [None])[0]
            search = query_params.get('search', [None])[0]
            
            npcs = npcs_data
            
            if category:
                npcs = [npc for npc in npcs if npc['category'] == category]
            
            if search:
                search_lower = search.lower()
                npcs = [npc for npc in npcs if 
                       search_lower in npc['name'].lower() or
                       search_lower in npc['code'].lower() or
                       search_lower in npc['type'].lower()]
            
            response = {'npcs': npcs}
            self.send_json_response(response)
            
        except FileNotFoundError:
            # Fallback to example data if file doesn't exist
            npcs = {
                'npcs': [
                    {
                        'name': 'Bear',
                        'code': 'BP_Bear',
                        'spawnCommand': '#SpawnCharacter BP_Bear',
                        'category': 'Animals',
                        'type': 'Predator'
                    }
                ]
            }
            self.send_json_response(npcs)
    
    def serve_search_api(self, query_params):
        """Serve global search results"""
        query = query_params.get('q', [None])[0]
        if not query:
            self.send_json_response({'results': []})
            return
        
        # Simulate search across all data types
        results = [
            {
                'type': 'item',
                'name': 'AK-74 Assault Rifle',
                'code': 'AK74_Rifle',
                'url': 'items.html#ak74',
                'category': 'weapons'
            },
            {
                'type': 'command',
                'name': 'SpawnItem',
                'syntax': '#SpawnItem <ItemCode>',
                'url': 'commands.html#spawnitem',
                'category': 'spawning'
            }
        ]
        
        # Filter results based on query
        filtered_results = [r for r in results if 
                          query.lower() in r['name'].lower() or
                          query.lower() in r.get('code', '').lower()]
        
        self.send_json_response({'results': filtered_results})
    
    def serve_stats_api(self):
        """Serve database statistics"""
        stats = {
            'items': 2000,
            'vehicles': 50,
            'commands': 100,
            'npcs': 75,
            'last_updated': '2025-01-15',
            'version': '2.0.1'
        }
        self.send_json_response(stats)
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        json_data = json.dumps(data, indent=2, ensure_ascii=False)
        self.wfile.write(json_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Custom log message format"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def find_free_port(start_port=8000):
    """Find a free port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError("No free ports available")

def open_browser(url, delay=2):
    """Open browser after a delay"""
    def open_after_delay():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"✓ Opened browser: {url}")
        except Exception as e:
            print(f"✗ Failed to open browser: {e}")
            print(f"  Please manually open: {url}")
    
    thread = threading.Thread(target=open_after_delay)
    thread.daemon = True
    thread.start()

def main():
    """Main server function"""
    print("="*60)
    print("🎮 SCUM Commands 2025 - Local Server")
    print("="*60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    if script_dir:
        os.chdir(script_dir)
        print(f"📁 Working directory: {script_dir}")
    
    # Find free port
    try:
        PORT = find_free_port(8000)
    except RuntimeError as e:
        print(f"✗ Error: {e}")
        sys.exit(1)
    
    # Create server
    try:
        with socketserver.TCPServer(("localhost", PORT), SCUMHTTPRequestHandler) as httpd:
            server_url = f"http://localhost:{PORT}"
            
            print(f"🚀 Server starting on: {server_url}")
            print(f"📊 Serving SCUM Commands database")
            print(f"🔗 Open in browser: {server_url}")
            print()
            print("📋 Available pages:")
            print(f"   • Home:     {server_url}/")
            print(f"   • Items:    {server_url}/items.html")
            print(f"   • Commands: {server_url}/commands.html")
            print(f"   • Vehicles: {server_url}/vehicles.html")
            print(f"   • NPCs:     {server_url}/npcs.html")
            print()
            print("🔧 API endpoints:")
            print(f"   • Items:    {server_url}/api/items")
            print(f"   • Vehicles: {server_url}/api/vehicles")
            print(f"   • Commands: {server_url}/api/commands")
            print(f"   • NPCs:     {server_url}/api/npcs")
            print(f"   • Search:   {server_url}/api/search")
            print(f"   • Stats:    {server_url}/api/stats")
            print()
            print("⌨️  Press Ctrl+C to stop the server")
            print("="*60)
            
            # Auto-open browser
            open_browser(server_url)
            
            # Start server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"✗ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 