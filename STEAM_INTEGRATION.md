# Steam Web API Integration for SCUM Commands 2025

This document explains how the Steam Web API integration works and how to set it up.

## Overview

The Steam Web API integration provides real-time SCUM game data including:

- **Current Player Count** - Live count of players currently playing SCUM
- **Game News** - Latest SCUM news and updates from Steam
- **Store Information** - Price, reviews, and store details
- **Server Status** - Mock server status for popular SCUM servers
- **Review Scores** - Community review summaries

## Architecture

The integration uses a two-tier architecture:

1. **Frontend JavaScript** (`js/steam-api.js`) - Handles UI integration and data display
2. **Backend Python Proxy** (`api/steam-proxy.py`) - Handles Steam API calls and CORS issues

### Why a Backend Proxy?

Steam's Web API has CORS restrictions that prevent direct calls from browsers. The backend proxy:
- Handles CORS issues
- Implements intelligent caching (5 minutes for most data)
- Rate limiting (60 requests per minute)
- Error handling and fallback data
- SQLite-based cache for performance

## Setup Instructions

### 1. Prerequisites

- Python 3.7 or higher
- pip (Python package installer)

### 2. Quick Start

1. **Start the Backend:**
   ```bash
   # On Windows
   start-steam-api.bat
   
   # On Linux/Mac
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   python api/steam-proxy.py
   ```

2. **Open the Website:**
   - Open `index.html` in your browser
   - The Steam integration will automatically detect if the backend is running
   - If backend is unavailable, it will show mock data

### 3. Manual Setup

1. **Create Virtual Environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start Backend Server:**
   ```bash
   python api/steam-proxy.py
   ```

4. **Verify Backend:**
   - Visit http://localhost:5001/api/steam/health
   - Should return: `{"status": "healthy", "timestamp": "...", "scum_app_id": "513710"}`

## API Endpoints

The backend provides these endpoints:

### Core Data
- `GET /api/steam/players` - Current player count
- `GET /api/steam/news?count=5` - Game news (default 5 items)
- `GET /api/steam/store` - Store information
- `GET /api/steam/reviews` - Review summary
- `GET /api/steam/comprehensive` - All data in one request

### Utility
- `GET /api/steam/health` - Health check
- `GET /api/steam/cache/stats` - Cache statistics
- `POST /api/steam/cache/clear` - Clear cache

## Frontend Integration

### Basic Usage

```javascript
// The Steam integration initializes automatically
// But you can also manually create instances:

const steamAPI = new SteamAPI();
const steamUI = new SteamUIIntegration();

// Get current players
const playerCount = await steamAPI.getCurrentPlayers();
console.log(`${playerCount} players online`);

// Get news
const news = await steamAPI.getGameNews(5);
news.forEach(item => console.log(item.title));
```

### UI Components

The integration adds these UI elements:

1. **Steam Stats Widget** - Shows player count, reviews, price
2. **Live SCUM Stats Section** - Main section with all Steam data
3. **Player Count in Hero Stats** - Real-time count in the stats bar
4. **News Feed** - Latest SCUM news items
5. **Server Status** - Mock server status display

### Search Integration

Steam data is integrated into the global search:
- Type "steam", "player", or "server" to see Steam-related suggestions
- Quick access to Steam sections and data

## Configuration

### Backend Configuration

Edit `api/steam-proxy.py` to modify:

```python
SCUM_APP_ID = '513710'        # SCUM's Steam App ID
CACHE_DURATION = 300          # 5 minutes cache
RATE_LIMIT_REQUESTS = 60      # 60 requests per minute
RATE_LIMIT_WINDOW = 60        # 1 minute window
```

### Frontend Configuration

Edit `js/steam-api.js` to modify:

```javascript
this.baseURL = 'http://localhost:5001/api/steam'; // Backend URL
this.cacheTimeout = 5 * 60 * 1000;               // 5 minutes cache
this.minRequestInterval = 1000;                   // 1 second between requests
```

## Features

### 🔄 Automatic Fallback
- If backend is unavailable, shows mock data
- Graceful degradation - website works without Steam data
- Health checks every page load

### ⚡ Performance Optimized
- Intelligent caching (frontend + backend)
- Rate limiting to respect Steam API limits
- Batch requests where possible
- Lazy loading of non-critical data

### 🎨 Beautiful UI
- Glassmorphism design with blur effects
- Smooth animations and hover effects
- Responsive design for all devices
- Dark/light theme support

### 🔍 Search Integration
- Steam data appears in search suggestions
- Quick navigation to Steam sections
- Keyboard shortcuts (Ctrl+S+S for Steam stats)

## Troubleshooting

### Backend Not Starting
```bash
# Check Python version
python --version

# Check if port 5001 is in use
netstat -an | grep 5001

# Try different port
PORT=5002 python api/steam-proxy.py
```

### CORS Issues
- Make sure backend is running on localhost:5001
- Check browser console for specific CORS errors
- Verify backend allows CORS (Flask-CORS should handle this)

### No Data Showing
1. Check browser console for errors
2. Verify backend health: http://localhost:5001/api/steam/health
3. Check cache: http://localhost:5001/api/steam/cache/stats
4. Clear cache: POST http://localhost:5001/api/steam/cache/clear

### Rate Limiting
- Backend implements 60 requests per minute limit
- Frontend caches for 5 minutes
- Steam API has its own rate limits

## Development

### Adding New Steam Data

1. **Add Backend Endpoint:**
   ```python
   @app.route('/api/steam/newdata', methods=['GET'])
   def get_new_data():
       # Implementation
   ```

2. **Add Frontend Method:**
   ```javascript
   async getNewData() {
       // Implementation
   }
   ```

3. **Update UI Integration:**
   ```javascript
   displayNewData(data) {
       // UI update logic
   }
   ```

### Testing

```bash
# Test backend endpoints
curl http://localhost:5001/api/steam/health
curl http://localhost:5001/api/steam/players
curl http://localhost:5001/api/steam/news

# Test with different parameters
curl "http://localhost:5001/api/steam/news?count=10"
```

## Production Deployment

### Backend Deployment
- Use production WSGI server (gunicorn, uWSGI)
- Set up reverse proxy (nginx)
- Configure SSL/HTTPS
- Set up monitoring and logging
- Use environment variables for configuration

### Frontend Deployment
- Update backend URL in `steam-api.js`
- Minify JavaScript files
- Set up CDN for static assets
- Configure caching headers

## License

This Steam integration is part of the SCUM Commands 2025 project and follows the same license terms.

## Support

For issues related to Steam integration:
1. Check this documentation
2. Review browser console for errors
3. Check backend logs
4. Report issues with detailed error messages

---

**Note:** This integration is not affiliated with Valve, Steam, or SCUM developers. It uses publicly available Steam Web API endpoints in accordance with Steam's terms of service. 