/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Primary Colors */
    --primary-color: #ff6b35;
    --primary-hover: #e55a2b;
    --primary-light: #ff8a5b;
    --primary-dark: #d4491f;
    
    /* Secondary Colors */
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --secondary-dark: #1a252f;
    
    /* Accent Colors */
    --accent-color: #3498db;
    --accent-hover: #2980b9;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: #1a1a1a;
    --bg-card: #ffffff;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-muted: #95a5a6;
    --text-light: #ecf0f1;
    --text-white: #ffffff;
    
    /* Border Colors */
    --border-color: #dee2e6;
    --border-light: #e9ecef;
    --border-dark: #495057;
    
    /* Shadow */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'Fira Code', 'Monaco', 'Menlo', monospace;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --bg-card: #2d2d2d;
    
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    
    --border-color: #404040;
    --border-light: #333333;
    --border-dark: #555555;
}

/* Base Elements */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (min-width: 768px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
}

/* Navigation */
.navbar {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .navbar {
    background-color: rgba(26, 26, 26, 0.95);
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--spacing-md);
    padding-bottom: var(--spacing-md);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.navbar-logo {
    width: 32px;
    height: 32px;
}

.navbar-badge {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.navbar-menu {
    display: none;
    gap: var(--spacing-lg);
}

.navbar-item {
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.navbar-item:hover,
.navbar-item.active {
    color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.navbar-toggle {
    display: flex;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.navbar-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

@media (min-width: 768px) {
    .navbar-menu {
        display: flex;
    }
    
    .navbar-toggle {
        display: none;
    }
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(10px, 10px); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.hero-title .highlight {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version-tag {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 600;
    margin-left: var(--spacing-sm);
    backdrop-filter: blur(10px);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    display: block;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-search {
    position: relative;
    max-width: 500px;
}

.search-container {
    position: relative;
    display: flex;
}

.search-input {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-primary);
    backdrop-filter: blur(10px);
    outline: none;
    transition: all var(--transition-fast);
}

.search-input:focus {
    background: white;
    box-shadow: var(--shadow-lg);
}

.search-button {
    position: absolute;
    right: var(--spacing-sm);
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-color);
    border: none;
    color: white;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-button:hover {
    background: var(--primary-hover);
}

@media (min-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr 1fr;
        align-items: center;
    }
    
    .hero-title {
        font-size: 4rem;
    }
}

/* Quick Access Section */
.quick-access {
    padding: var(--spacing-2xl) 0;
    background-color: var(--bg-secondary);
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.card {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-light);
}

.quick-access-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-access-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-slow);
}

.quick-access-card:hover::before {
    left: 100%;
}

.quick-access-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    color: white;
    font-size: 1.5rem;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.card-stats {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.stat-badge {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
}

.stat-badge.new {
    background: var(--success-color);
    color: white;
}

.stat-badge.hot {
    background: var(--danger-color);
    color: white;
}

/* Features Section */
.features {
    padding: var(--spacing-2xl) 0;
    background-color: var(--bg-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-item {
    text-align: center;
    padding: var(--spacing-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: white;
    font-size: 2rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Footer */
.footer {
    background-color: var(--secondary-color);
    color: var(--text-light);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-title {
    color: white;
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
}

.footer-subtitle {
    color: white;
    font-size: 1.125rem;
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: var(--text-muted);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid var(--secondary-light);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-copyright {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.footer-meta {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.meta-item {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 767px) {
    .hero {
        padding: 100px 0 60px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .cards-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.items-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; } 