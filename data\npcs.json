[{"name": "Unknown", "code": "BP_BaseComplexAnimal2", "spawnCommand": "#SpawnCharacter BP_BaseComplexAnimal2", "category": "Animals", "type": "Unknown"}, {"name": "Bear", "code": "BP_Bear", "spawnCommand": "#SpawnCharacter BP_Bear", "category": "Animals", "type": "Predator"}, {"name": "Bear", "code": "BP_Bear2", "spawnCommand": "#SpawnCharacter BP_Bear2", "category": "Animals", "type": "Predator"}, {"name": "Crow", "code": "BP_Bird2", "spawnCommand": "#SpawnCharacter BP_Bird2", "category": "Animals", "type": "<PERSON>"}, {"name": "<PERSON><PERSON>", "code": "BP_Boar", "spawnCommand": "#SpawnCharacter BP_Boar", "category": "Animals", "type": "Wildlife"}, {"name": "Cat", "code": "BP_Cat", "spawnCommand": "#SpawnCharacter BP_Cat", "category": "Animals", "type": "Domestic"}, {"name": "Chicken", "code": "BP_Chicken", "spawnCommand": "#SpawnCharacter BP_Chicken", "category": "Animals", "type": "Domestic"}, {"name": "Bear (with cow sounds)", "code": "BP_Cow2", "spawnCommand": "#SpawnCharacter BP_Cow2", "category": "Animals", "type": "Predator"}, {"name": "<PERSON><PERSON>", "code": "BP_Crab", "spawnCommand": "#SpawnCharacter BP_Crab", "category": "Animals", "type": "Marine"}, {"name": "Crow", "code": "BP_Crow", "spawnCommand": "#SpawnCharacter BP_Crow", "category": "Animals", "type": "<PERSON>"}, {"name": "Deer", "code": "BP_Deer", "spawnCommand": "#SpawnCharacter BP_Deer", "category": "Animals", "type": "Wildlife"}, {"name": "Stag", "code": "BP_Deer2", "spawnCommand": "#SpawnCharacter BP_Deer2", "category": "Animals", "type": "Wildlife"}, {"name": "<PERSON><PERSON>", "code": "BP_Donkey2", "spawnCommand": "#SpawnCharacter BP_Donkey2", "category": "Animals", "type": "Domestic"}, {"name": "Drone", "code": "BP_Drone", "spawnCommand": "#SpawnCharacter BP_Drone", "category": "Mechanical", "type": "Drone"}, {"name": "Fish", "code": "BP_Fish", "spawnCommand": "#SpawnCharacter BP_Fish", "category": "Animals", "type": "Marine"}, {"name": "Goa<PERSON>", "code": "BP_Goat2", "spawnCommand": "#SpawnCharacter BP_Goat2", "category": "Animals", "type": "Domestic"}, {"name": "Hen", "code": "BP_Hen", "spawnCommand": "#SpawnCharacter BP_Hen", "category": "Animals", "type": "Domestic"}, {"name": "Horse", "code": "BP_Horse2", "spawnCommand": "#SpawnCharacter BP_Horse2", "category": "Animals", "type": "Domestic"}, {"name": "Boar (with pig sounds)", "code": "BP_Pig", "spawnCommand": "#SpawnCharacter BP_Pig", "category": "Animals", "type": "Wildlife"}, {"name": "<PERSON>eon", "code": "BP_Pigeon", "spawnCommand": "#SpawnCharacter BP_Pigeon", "category": "Animals", "type": "<PERSON>"}, {"name": "Player Character", "code": "BP_Prisoner", "spawnCommand": "#SpawnCharacter BP_Prisoner", "category": "Human", "type": "Player"}, {"name": "Rabbit", "code": "BP_Rabbit", "spawnCommand": "#SpawnCharacter BP_Rabbit", "category": "Animals", "type": "Small Wildlife"}, {"name": "Rabbit", "code": "BP_Rabbit2", "spawnCommand": "#SpawnCharacter BP_Rabbit2", "category": "Animals", "type": "Small Wildlife"}, {"name": "Rat", "code": "BP_Rat2", "spawnCommand": "#SpawnCharacter BP_Rat2", "category": "Animals", "type": "Small Wildlife"}, {"name": "Raven", "code": "BP_Raven", "spawnCommand": "#SpawnCharacter BP_Raven", "category": "Animals", "type": "<PERSON>"}, {"name": "Seagull", "code": "BP_Seagull", "spawnCommand": "#SpawnCharacter BP_Seagull", "category": "Animals", "type": "<PERSON>"}, {"name": "Sentry", "code": "BP_Sentry", "spawnCommand": "#SpawnCharacter BP_Sentry", "category": "Mechanical", "type": "Sentry"}, {"name": "Sentry", "code": "BP_SentryWithThirdPersonView", "spawnCommand": "#SpawnCharacter BP_SentryWithThirdPersonView", "category": "Mechanical", "type": "Sentry"}, {"name": "Goat (with sheep sounds)", "code": "BP_Sheep2", "spawnCommand": "#SpawnCharacter BP_Sheep2", "category": "Animals", "type": "Domestic"}, {"name": "Snake", "code": "BP_Snake", "spawnCommand": "#SpawnCharacter BP_Snake", "category": "Animals", "type": "Reptile"}, {"name": "<PERSON>", "code": "BP_Swan", "spawnCommand": "#SpawnCharacter BP_Swan", "category": "Animals", "type": "<PERSON>"}, {"name": "<PERSON> (test model)", "code": "BP_TestRabbit", "spawnCommand": "#SpawnCharacter BP_TestRabbit", "category": "Animals", "type": "Small Wildlife"}, {"name": "<PERSON> (<PERSON>)", "code": "BP_WhiteDuck", "spawnCommand": "#SpawnCharacter BP_WhiteDuck", "category": "Animals", "type": "<PERSON>"}, {"name": "<PERSON>", "code": "BP_Wolf", "spawnCommand": "#SpawnCharacter BP_Wolf", "category": "Animals", "type": "Predator"}, {"name": "Donkey (with wolf sounds)", "code": "BP_Wolf3", "spawnCommand": "#SpawnCharacter BP_Wolf3", "category": "Animals", "type": "Domestic"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BP_Zombie2", "spawnCommand": "#SpawnCharacter BP_Zombie2", "category": "Zombie", "type": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BP_Zombie2_Player", "spawnCommand": "#SpawnCharacter BP_Zombie2_Player", "category": "Zombie", "type": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "code": "BP_Zombie2_Test", "spawnCommand": "#SpawnCharacter BP_Zombie2_Test", "category": "Zombie", "type": "<PERSON><PERSON><PERSON>"}, {"name": "Wild Duck", "code": "WildDuck", "spawnCommand": "#SpawnCharacter WildDuck", "category": "Animals", "type": "<PERSON>"}, {"name": "ZF", "code": "ZF", "spawnCommand": "#SpawnCharacter ZF", "category": "Zombie", "type": "Zombie"}, {"name": "ZN", "code": "ZN", "spawnCommand": "#SpawnCharacter ZN", "category": "Zombie", "type": "Zombie"}, {"name": "ZS", "code": "ZS", "spawnCommand": "#SpawnCharacter ZS", "category": "Zombie", "type": "Zombie"}]