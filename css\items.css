/* Items Page CSS for SCUM Commands 2025 */

/* Items Page Layout */
.items-page {
    padding-top: 80px; /* Account for fixed navbar */
    min-height: 100vh;
    background-color: var(--bg-primary);
}

.items-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-xl) 0;
    text-align: center;
}

.items-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.items-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Search and Filter Section */
.search-filter-section {
    background-color: var(--bg-secondary);
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--border-color);
}

.search-filter-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

@media (min-width: 768px) {
    .search-filter-container {
        grid-template-columns: 2fr 1fr;
    }
}

.search-section {
    position: relative;
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    padding-left: 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background-color: var(--bg-card);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1.125rem;
}

.filter-section {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    min-width: 120px;
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-card);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
}

/* View Toggle */
.view-toggle {
    display: flex;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-left: auto;
}

.view-toggle-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.view-toggle-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.view-toggle-btn:hover:not(.active) {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Items Content */
.items-content {
    padding: var(--spacing-xl) 0;
}

.items-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.items-count {
    font-weight: 600;
    color: var(--text-primary);
}

.items-showing {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Table View */
.table-view {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.items-table {
    width: 100%;
    border-collapse: collapse;
}

.items-table th {
    background-color: var(--bg-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.items-table td {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.items-table tr:hover {
    background-color: var(--bg-secondary);
}

.item-image {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-sm);
    object-fit: cover;
    background-color: var(--bg-secondary);
}

.item-name {
    font-weight: 600;
    color: var(--text-primary);
}

.item-code {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.item-category {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-rarity {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.item-rarity.common {
    background-color: var(--success-color);
    color: white;
}

.item-rarity.uncommon {
    background-color: var(--warning-color);
    color: white;
}

.item-rarity.rare {
    background-color: var(--danger-color);
    color: white;
}

.item-rarity.legendary {
    background-color: #9b59b6;
    color: white;
}

.copy-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.copy-btn:hover {
    background-color: var(--accent-hover);
    transform: translateY(-1px);
}

/* Card View */
.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.item-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.item-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.item-card-image {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    object-fit: cover;
    background-color: var(--bg-secondary);
}

.item-card-info h3 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.item-card-code {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.item-card-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
}

.item-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.item-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Loading State */
.items-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.items-loading .spinner {
    margin-right: var(--spacing-md);
}

/* Empty State */
.items-empty {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.items-empty i {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.items-empty h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 767px) {
    .items-title {
        font-size: 2rem;
    }
    
    .search-filter-container {
        grid-template-columns: 1fr;
    }
    
    .filter-section {
        justify-content: center;
    }
    
    .view-toggle {
        margin: 0 auto;
    }
    
    .items-stats {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .items-table {
        font-size: 0.875rem;
    }
    
    .items-table th,
    .items-table td {
        padding: var(--spacing-sm);
    }
    
    .card-view {
        grid-template-columns: 1fr;
    }
}

/* Toast for Copy Feedback */
.copy-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--success-color);
    color: white;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1060;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.copy-toast.show {
    transform: translateX(0);
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
} 