# SCUM Commands 2025 - Complete Database Website

A comprehensive, modern website for SCUM game admin commands, items, vehicles, and NPCs with advanced search, filtering, and Steam integration.

## 🚀 Features

### Core Functionality
- **Complete Admin Commands Database**: All 19+ SCUM admin commands with syntax, parameters, and examples
- **Comprehensive Items Database**: 500+ items with spawn codes, images, categories, and detailed information
- **Vehicle Spawn Codes**: All vehicle types with spawn commands and specifications
- **NPC Database**: 42+ NPCs including animals, zombies, and mechanical entities with spawn codes

### Advanced Features
- **Real-time Search**: Instant search across all databases with autocomplete
- **Smart Filtering**: Filter by category, type, rarity, and other criteria
- **One-Click Copy**: Copy spawn commands directly to clipboard
- **Multiple View Modes**: Switch between table and card views
- **Steam Integration**: Optional Steam API integration for enhanced features
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Fast Performance**: Optimized loading and caching for quick access

## 📁 Project Structure

```
new-scum-website/
├── index.html              # Homepage with search and navigation
├── commands.html           # Admin commands database
├── items.html              # Items database with advanced filtering
├── vehicles.html           # Vehicle spawn codes
├── npcs.html              # NPC spawn codes
├── server.py              # Python HTTP server with API endpoints
├── start-server.bat       # Windows batch file to start server
├── start-steam-api.bat    # Windows batch file for Steam API
├── requirements.txt       # Python dependencies
├── css/
│   ├── main.css           # Main website styles
│   └── steam-integration.css # Steam-specific styles
├── js/
│   ├── main.js            # Core JavaScript functionality
│   └── steam-api.js       # Steam integration features
├── data/
│   ├── items.json         # Items database (500+ items)
│   ├── commands.json      # Commands database (19+ commands)
│   ├── vehicles.json      # Vehicles database (5+ vehicles)
│   └── npcs.json          # NPCs database (42+ NPCs)
├── api/
│   └── steam-proxy.py     # Steam API proxy server
├── venv/                  # Python virtual environment
└── steam_cache.db         # Steam data cache (auto-generated)
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.7 or higher
- Modern web browser
- Internet connection (for Steam integration)

### Quick Start

1. **Clone or extract the project**
   ```bash
   cd new-scum-website
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```
   Or use the virtual environment:
   ```bash
   # On Windows
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Start the server**
   ```bash
   python server.py
   ```
   Or on Windows, double-click `start-server.bat`

4. **Access the website**
   - Open your browser to `http://localhost:8000`
   - The server will automatically find an available port if 8000 is taken

### Steam Integration (Optional)

For enhanced Steam features:

1. **Start the Steam API proxy**
   ```bash
   python api/steam-proxy.py
   ```
   Or on Windows, double-click `start-steam-api.bat`

2. **The Steam integration provides**:
   - Steam profile integration
   - Enhanced user features
   - Community features

## 📊 Database Information

### Commands Database (commands.json)
- **19+ Admin Commands** with complete documentation
- Categories: General, Player Management, Spawning, Teleportation, Server Management, Environment, Debug
- Each command includes: name, description, syntax, parameters, examples, category

### Items Database (items.json)
- **500+ Items** from SCUM game
- Categories: Weapons, Armor, Food, Tools, Medical, Electronics, etc.
- Each item includes: name, code, description, category, rarity, spawn command, images
- Advanced filtering by category, rarity, year added, and tags

### Vehicles Database (vehicles.json)
- **5+ Vehicle Types** with spawn codes
- Categories: SUVs, Military vehicles, Civilian vehicles
- Each vehicle includes: name, code, spawn command, category, type

### NPCs Database (npcs.json)
- **42+ NPCs** including animals, zombies, and mechanical entities
- Categories: Animals, Zombies, Mechanical, Human
- Types: Predators, Wildlife, Domestic animals, Birds, Marine life, etc.
- Each NPC includes: name, code, spawn command, category, type

## 🎮 Usage Guide

### Searching
- Use the global search bar on the homepage for quick access
- Each page has dedicated search functionality
- Search supports partial matches and multiple terms

### Filtering
- **Commands**: Filter by category (General, Spawning, etc.)
- **Items**: Filter by category, rarity, year added
- **Vehicles**: Filter by type (SUV, etc.)
- **NPCs**: Filter by category (Animals, Zombies, etc.)

### Copying Commands
- Click any "Copy" button to copy spawn commands to clipboard
- Commands are formatted correctly for SCUM console use
- Includes the # prefix required by SCUM

### View Modes
- **Table View**: Compact list format with sortable columns
- **Card View**: Detailed cards with additional information

## 🔧 Technical Details

### Server Features
- **Python HTTP Server**: Custom server with API endpoints
- **CORS Support**: Enables cross-origin requests
- **Static File Serving**: Serves HTML, CSS, JS, and assets
- **API Endpoints**: RESTful API for all data types
- **Error Handling**: Graceful fallbacks and error messages

### API Endpoints
- `GET /api/items` - Items database with filtering
- `GET /api/commands` - Commands database
- `GET /api/vehicles` - Vehicles database
- `GET /api/npcs` - NPCs database
- `GET /api/search` - Global search across all databases
- `GET /api/stats` - Database statistics

### Performance Optimizations
- **Efficient Data Loading**: JSON-based data storage
- **Client-side Caching**: Reduces server requests
- **Optimized Search**: Fast filtering and search algorithms
- **Responsive Images**: Optimized image loading
- **Minified Assets**: Compressed CSS and JavaScript

## 🎨 Design Features

### Modern UI/UX
- **Bootstrap 5**: Modern, responsive framework
- **Font Awesome Icons**: Professional iconography
- **Custom Animations**: Smooth transitions and effects
- **Dark/Light Themes**: Automatic theme detection
- **Mobile-First Design**: Optimized for all screen sizes

### Accessibility
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels
- **High Contrast**: Accessible color schemes
- **Responsive Text**: Scalable font sizes

## 🔄 Data Updates

### Adding New Data
1. **Items**: Add to `data/items.json` following the existing format
2. **Commands**: Add to `data/commands.json` with parameters and examples
3. **Vehicles**: Add to `data/vehicles.json` with spawn codes
4. **NPCs**: Add to `data/npcs.json` with categories and types

### Data Format Examples

**Item Format**:
```json
{
  "name": "AK-74 Assault Rifle",
  "code": "BP_Rifle_AK74",
  "description": "High-damage assault rifle",
  "category": "weapons",
  "rarity": "rare",
  "spawnCommand": "#SpawnItem BP_Rifle_AK74",
  "image32": "images/items/32/ak74.png",
  "image64": "images/items/64/ak74.png"
}
```

**Command Format**:
```json
{
  "name": "SpawnItem",
  "description": "Spawn an item in front of your character",
  "syntax": "#SpawnItem [Item Code] [Amount]",
  "parameters": [...],
  "example": "#SpawnItem BP_Rifle_AK74 1",
  "category": "Spawning"
}
```

## 🚀 Deployment

### Local Development
- Use the included Python server for development
- Automatic port detection and browser opening
- Hot reload for CSS/JS changes

### Production Deployment
- Can be deployed to any web server (Apache, Nginx, etc.)
- Static files can be served directly
- Python server can be used with WSGI adapters
- Consider using a reverse proxy for production

### Docker Support
Create a `Dockerfile` for containerized deployment:
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "server.py"]
```

## 🤝 Contributing

### How to Contribute
1. **Data Updates**: Submit new items, commands, vehicles, or NPCs
2. **Bug Reports**: Report issues or inconsistencies
3. **Feature Requests**: Suggest new functionality
4. **Code Improvements**: Submit optimizations or fixes

### Data Sources
- Official SCUM game files
- Community contributions
- Game update changelogs
- Developer announcements

## 📝 License

This project is not affiliated with Gamepires or Devolver Digital. All SCUM-related content belongs to their respective owners.

## 🎯 Roadmap

### Planned Features
- [ ] Advanced admin tools and generators
- [ ] Server configuration templates
- [ ] Community user submissions
- [ ] Mobile app version
- [ ] Offline mode support
- [ ] Multi-language support

### Recent Updates
- ✅ Complete database migration from original site
- ✅ Modern responsive design implementation
- ✅ Steam integration features
- ✅ Advanced search and filtering
- ✅ API endpoints for all data types
- ✅ One-click copy functionality

## 📞 Support

For support, questions, or contributions:
- Check the existing data files for format examples
- Test thoroughly before submitting changes
- Ensure all spawn codes are valid and current
- Follow the existing naming conventions

---

**Built with ❤️ for the SCUM community** 