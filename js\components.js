// Components JavaScript for SCUM Commands 2025

// Toast notification system
class ToastManager {
    constructor() {
        this.container = this.createContainer();
        this.toasts = new Map();
    }

    createContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1060;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, type = 'info', duration = 3000) {
        const toast = this.createToast(message, type);
        this.container.appendChild(toast);
        
        // Trigger animation
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // Auto-hide
        const timeoutId = setTimeout(() => {
            this.hide(toast.id);
        }, duration);

        this.toasts.set(toast.id, { element: toast, timeoutId });
        return toast.id;
    }

    createToast(message, type) {
        const toast = document.createElement('div');
        toast.id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            transition: transform var(--transition-normal);
            pointer-events: auto;
            max-width: 300px;
            word-wrap: break-word;
        `;

        const icon = this.getIcon(type);
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                <i class="${icon}" style="color: ${this.getColor(type)};"></i>
                <span>${message}</span>
                <button onclick="toastManager.hide('${toast.id}')" style="
                    background: none;
                    border: none;
                    color: var(--text-secondary);
                    cursor: pointer;
                    margin-left: auto;
                    padding: 2px;
                ">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        return toast;
    }

    hide(toastId) {
        const toastData = this.toasts.get(toastId);
        if (!toastData) return;

        const { element, timeoutId } = toastData;
        clearTimeout(timeoutId);

        element.classList.remove('show');
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            this.toasts.delete(toastId);
        }, 300);
    }

    getIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    getColor(type) {
        const colors = {
            success: 'var(--success-color)',
            error: 'var(--danger-color)',
            warning: 'var(--warning-color)',
            info: 'var(--accent-color)'
        };
        return colors[type] || colors.info;
    }
}

// Modal system
class ModalManager {
    constructor() {
        this.activeModal = null;
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.hide();
            }
        });
    }

    show(title, content, options = {}) {
        this.hide(); // Close any existing modal

        const modal = this.createModal(title, content, options);
        document.body.appendChild(modal);
        this.activeModal = modal;

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Trigger animation
        requestAnimationFrame(() => {
            modal.classList.add('show');
        });

        return modal;
    }

    createModal(title, content, options) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="modal-close" onclick="modalManager.hide()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${options.showFooter !== false ? `
                    <div class="modal-footer">
                        ${options.footerContent || `
                            <button class="btn btn-secondary" onclick="modalManager.hide()">Close</button>
                        `}
                    </div>
                ` : ''}
            </div>
        `;

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hide();
            }
        });

        return modal;
    }

    hide() {
        if (!this.activeModal) return;

        this.activeModal.classList.remove('show');
        
        setTimeout(() => {
            if (this.activeModal && this.activeModal.parentNode) {
                this.activeModal.parentNode.removeChild(this.activeModal);
            }
            this.activeModal = null;
            document.body.style.overflow = '';
        }, 300);
    }
}

// Loading overlay
class LoadingManager {
    constructor() {
        this.overlay = null;
    }

    show(message = 'Loading...') {
        this.hide(); // Remove any existing overlay

        this.overlay = document.createElement('div');
        this.overlay.className = 'loading-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1070;
            backdrop-filter: blur(2px);
        `;

        this.overlay.innerHTML = `
            <div style="
                background: var(--bg-card);
                padding: var(--spacing-xl);
                border-radius: var(--radius-lg);
                box-shadow: var(--shadow-xl);
                text-align: center;
                min-width: 200px;
            ">
                <div class="spinner" style="margin: 0 auto var(--spacing-md) auto;"></div>
                <div style="color: var(--text-primary); font-weight: 500;">${message}</div>
            </div>
        `;

        document.body.appendChild(this.overlay);
        document.body.style.overflow = 'hidden';
    }

    hide() {
        if (this.overlay) {
            document.body.removeChild(this.overlay);
            this.overlay = null;
            document.body.style.overflow = '';
        }
    }
}

// Copy to clipboard utility
class ClipboardManager {
    static async copy(text, successMessage = 'Copied to clipboard!') {
        try {
            await navigator.clipboard.writeText(text);
            toastManager.show(successMessage, 'success');
            return true;
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                toastManager.show(successMessage, 'success');
                return true;
            } catch (err) {
                toastManager.show('Failed to copy to clipboard', 'error');
                return false;
            } finally {
                document.body.removeChild(textArea);
            }
        }
    }
}

// Pagination component
class PaginationManager {
    constructor(container, options = {}) {
        this.container = container;
        this.currentPage = options.currentPage || 1;
        this.totalPages = options.totalPages || 1;
        this.maxVisible = options.maxVisible || 5;
        this.onPageChange = options.onPageChange || (() => {});
    }

    render() {
        if (this.totalPages <= 1) {
            this.container.innerHTML = '';
            return;
        }

        const pagination = document.createElement('div');
        pagination.className = 'pagination';

        // Previous button
        pagination.appendChild(this.createButton(
            '<i class="fas fa-chevron-left"></i>',
            this.currentPage - 1,
            this.currentPage === 1
        ));

        // Page numbers
        const startPage = Math.max(1, this.currentPage - Math.floor(this.maxVisible / 2));
        const endPage = Math.min(this.totalPages, startPage + this.maxVisible - 1);

        if (startPage > 1) {
            pagination.appendChild(this.createButton('1', 1));
            if (startPage > 2) {
                pagination.appendChild(this.createEllipsis());
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            pagination.appendChild(this.createButton(i, i, false, i === this.currentPage));
        }

        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                pagination.appendChild(this.createEllipsis());
            }
            pagination.appendChild(this.createButton(this.totalPages, this.totalPages));
        }

        // Next button
        pagination.appendChild(this.createButton(
            '<i class="fas fa-chevron-right"></i>',
            this.currentPage + 1,
            this.currentPage === this.totalPages
        ));

        this.container.innerHTML = '';
        this.container.appendChild(pagination);
    }

    createButton(text, page, disabled = false, active = false) {
        const button = document.createElement('button');
        button.className = `pagination-item ${disabled ? 'disabled' : ''} ${active ? 'active' : ''}`;
        button.innerHTML = text;
        button.disabled = disabled;

        if (!disabled) {
            button.addEventListener('click', () => {
                this.currentPage = page;
                this.onPageChange(page);
                this.render();
            });
        }

        return button;
    }

    createEllipsis() {
        const span = document.createElement('span');
        span.className = 'pagination-item disabled';
        span.textContent = '...';
        return span;
    }

    setPage(page) {
        this.currentPage = Math.max(1, Math.min(page, this.totalPages));
        this.render();
    }

    setTotalPages(total) {
        this.totalPages = total;
        this.currentPage = Math.min(this.currentPage, total);
        this.render();
    }
}

// Search component with debouncing
class SearchComponent {
    constructor(input, options = {}) {
        this.input = input;
        this.onSearch = options.onSearch || (() => {});
        this.debounceTime = options.debounceTime || 300;
        this.minLength = options.minLength || 2;
        this.debounceTimer = null;

        this.setupEventListeners();
    }

    setupEventListeners() {
        this.input.addEventListener('input', (e) => {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                const query = e.target.value.trim();
                if (query.length >= this.minLength || query.length === 0) {
                    this.onSearch(query);
                }
            }, this.debounceTime);
        });

        this.input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                clearTimeout(this.debounceTimer);
                this.onSearch(e.target.value.trim());
            }
        });
    }

    setValue(value) {
        this.input.value = value;
        this.onSearch(value);
    }

    clear() {
        this.input.value = '';
        this.onSearch('');
    }
}

// Initialize global instances
const toastManager = new ToastManager();
const modalManager = new ModalManager();
const loadingManager = new LoadingManager();

// Make managers available globally
window.toastManager = toastManager;
window.modalManager = modalManager;
window.loadingManager = loadingManager;
window.ClipboardManager = ClipboardManager;
window.PaginationManager = PaginationManager;
window.SearchComponent = SearchComponent;

// Utility functions
window.utils = {
    formatNumber: (num) => {
        if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
        if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
        return num.toString();
    },

    formatDate: (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    slugify: (text) => {
        return text
            .toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/[\s_-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    },

    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    throttle: (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}; 