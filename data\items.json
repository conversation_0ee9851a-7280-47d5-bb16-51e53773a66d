{"items": [{"id": "ak74_rifle", "name": "AK-74 Assault Rifle", "code": "AK74_Rifle", "description": "High-damage assault rifle with excellent range and reliability. Fires 5.45x39mm ammunition.", "category": "weapons", "subcategory": "assault_rifles", "rarity": "rare", "weight": 3.1, "durability": 100, "addedIn": "2025", "tags": ["automatic", "rifle", "military"], "ammoType": "5.45x39mm", "damage": 45, "range": 600, "image": "assets/items/ak74.png"}, {"id": "m4a1_carbine", "name": "M4A1 Carbine", "code": "M4A1_Carbine", "description": "NATO standard assault rifle with modular design and high accuracy.", "category": "weapons", "subcategory": "assault_rifles", "rarity": "rare", "weight": 2.9, "durability": 100, "addedIn": "2024", "tags": ["automatic", "rifle", "nato"], "ammoType": "5.56x45mm", "damage": 42, "range": 550, "image": "assets/items/m4a1.png"}, {"id": "tactical_vest", "name": "Tactical Vest", "code": "Tactical_Vest_01", "description": "Lightweight body armor providing moderate protection against ballistic threats.", "category": "armor", "subcategory": "chest_armor", "rarity": "uncommon", "weight": 1.5, "durability": 80, "addedIn": "2024", "tags": ["protection", "armor", "vest"], "armorValue": 35, "coverage": ["chest", "back"], "image": "assets/items/tactical_vest.png"}, {"id": "military_helmet", "name": "Military Helmet", "code": "Military_Helmet_01", "description": "Standard issue military helmet with ballistic protection.", "category": "armor", "subcategory": "helmets", "rarity": "uncommon", "weight": 1.2, "durability": 75, "addedIn": "2024", "tags": ["protection", "head", "military"], "armorValue": 25, "coverage": ["head"], "image": "assets/items/military_helmet.png"}, {"id": "canned_beans", "name": "Canned Beans", "code": "Canned_Beans", "description": "Nutritious canned food that restores moderate hunger and provides energy.", "category": "food", "subcategory": "canned_food", "rarity": "common", "weight": 0.4, "durability": 100, "addedIn": "2024", "tags": ["survival", "nutrition", "canned"], "hungerRestore": 45, "calories": 280, "image": "assets/items/canned_beans.png"}, {"id": "energy_drink", "name": "Energy Drink", "code": "Energy_Drink_01", "description": "High-caffeine beverage that temporarily boosts stamina and alertness.", "category": "food", "subcategory": "beverages", "rarity": "uncommon", "weight": 0.5, "durability": 100, "addedIn": "2025", "tags": ["beverage", "energy", "stamina"], "thirstRestore": 30, "staminaBoost": 25, "effects": ["increased_stamina_regen"], "image": "assets/items/energy_drink.png"}, {"id": "first_aid_kit", "name": "First Aid Kit", "code": "First_Aid_Kit", "description": "Complete medical kit for treating wounds and injuries in the field.", "category": "medical", "subcategory": "treatment", "rarity": "uncommon", "weight": 0.8, "durability": 100, "addedIn": "2024", "tags": ["medical", "healing", "emergency"], "healAmount": 75, "uses": 3, "image": "assets/items/first_aid_kit.png"}, {"id": "morphine_syrette", "name": "Mo<PERSON>hine Syrette", "code": "Morphine_Syrette", "description": "Emergency pain relief injection that provides temporary immunity to pain.", "category": "medical", "subcategory": "medication", "rarity": "rare", "weight": 0.1, "durability": 100, "addedIn": "2025", "tags": ["medical", "painkiller", "injection"], "painRelief": 100, "duration": 300, "effects": ["pain_immunity"], "image": "assets/items/morphine.png"}, {"id": "survival_knife", "name": "Survival Knife", "code": "Survival_Knife", "description": "Multi-purpose tactical knife essential for survival situations.", "category": "tools", "subcategory": "cutting_tools", "rarity": "common", "weight": 0.3, "durability": 150, "addedIn": "2024", "tags": ["tool", "cutting", "survival"], "damage": 25, "uses": ["cutting", "skinning", "crafting"], "image": "assets/items/survival_knife.png"}, {"id": "entrenching_tool", "name": "Entrenching Tool", "code": "Entrenching_Tool", "description": "Military folding shovel for digging and construction work.", "category": "tools", "subcategory": "construction", "rarity": "uncommon", "weight": 1.1, "durability": 200, "addedIn": "2024", "tags": ["tool", "digging", "construction"], "damage": 30, "uses": ["digging", "construction", "melee"], "image": "assets/items/entrenching_tool.png"}, {"id": "556_ammo", "name": "5.56x45mm NATO", "code": "556_Ammo", "description": "Standard NATO rifle ammunition compatible with assault rifles.", "category": "ammo", "subcategory": "rifle_ammo", "rarity": "uncommon", "weight": 0.012, "durability": 100, "addedIn": "2024", "tags": ["ammunition", "rifle", "nato"], "caliber": "5.56x45mm", "damage": 42, "penetration": 35, "image": "assets/items/556_ammo.png"}, {"id": "545_ammo", "name": "5.45x39mm Soviet", "code": "545_Ammo", "description": "Soviet rifle ammunition with high velocity and accuracy.", "category": "ammo", "subcategory": "rifle_ammo", "rarity": "uncommon", "weight": 0.01, "durability": 100, "addedIn": "2025", "tags": ["ammunition", "rifle", "soviet"], "caliber": "5.45x39mm", "damage": 45, "penetration": 38, "image": "assets/items/545_ammo.png"}, {"id": "gps_device", "name": "GPS Device", "code": "GPS_Device", "description": "Military-grade GPS navigation system with mapping capabilities.", "category": "electronics", "subcategory": "navigation", "rarity": "rare", "weight": 0.3, "durability": 100, "addedIn": "2025", "tags": ["navigation", "electronics", "military"], "batteryLife": 48, "accuracy": 95, "image": "assets/items/gps.png"}, {"id": "radio_transceiver", "name": "Radio Transceiver", "code": "Radio_Transceiver", "description": "Long-range communication device for coordinating with team members.", "category": "electronics", "subcategory": "communication", "rarity": "rare", "weight": 0.8, "durability": 90, "addedIn": "2025", "tags": ["communication", "electronics", "team"], "range": 5000, "batteryLife": 24, "image": "assets/items/radio.png"}, {"id": "military_backpack", "name": "Military Backpack", "code": "Military_Backpack", "description": "Large capacity tactical backpack for extended operations.", "category": "containers", "subcategory": "backpacks", "rarity": "uncommon", "weight": 1.2, "durability": 120, "addedIn": "2024", "tags": ["storage", "tactical", "large"], "capacity": 60, "slots": 24, "image": "assets/items/military_backpack.png"}, {"id": "ammo_box", "name": "Ammunition Box", "code": "Ammo_Box", "description": "Metal container specifically designed for storing ammunition safely.", "category": "containers", "subcategory": "storage", "rarity": "common", "weight": 2.5, "durability": 150, "addedIn": "2024", "tags": ["storage", "ammunition", "metal"], "capacity": 30, "specialization": "ammunition", "image": "assets/items/ammo_box.png"}, {"id": "duct_tape", "name": "Duct <PERSON>", "code": "Duct_Tape", "description": "Versatile adhesive tape essential for repairs and crafting.", "category": "crafting", "subcategory": "materials", "rarity": "common", "weight": 0.2, "durability": 100, "addedIn": "2024", "tags": ["crafting", "repair", "adhesive"], "uses": 10, "repairValue": 15, "image": "assets/items/duct_tape.png"}, {"id": "scrap_metal", "name": "Scrap Metal", "code": "Scrap_Metal", "description": "Salvaged metal pieces useful for crafting and construction.", "category": "crafting", "subcategory": "materials", "rarity": "common", "weight": 0.5, "durability": 100, "addedIn": "2024", "tags": ["crafting", "construction", "metal"], "craftingValue": 20, "image": "assets/items/scrap_metal.png"}, {"id": "night_vision_goggles", "name": "Night Vision Goggles", "code": "Night_Vision_Goggles", "description": "Advanced optical device enabling clear vision in low-light conditions.", "category": "electronics", "subcategory": "optics", "rarity": "legendary", "weight": 0.7, "durability": 80, "addedIn": "2025", "tags": ["optics", "night_vision", "advanced"], "visionRange": 100, "batteryLife": 12, "image": "assets/items/nvg.png"}, {"id": "thermal_scope", "name": "<PERSON><PERSON>", "code": "The<PERSON>_Scope", "description": "Heat-detecting scope attachment for precision shooting.", "category": "electronics", "subcategory": "weapon_attachments", "rarity": "epic", "weight": 0.9, "durability": 75, "addedIn": "2025", "tags": ["scope", "thermal", "precision"], "magnification": 4, "thermalRange": 500, "image": "assets/items/thermal_scope.png"}, {"id": "protein_bar", "name": "Protein Bar", "code": "Protein_Bar", "description": "High-protein nutrition bar for sustained energy during activities.", "category": "food", "subcategory": "nutrition", "rarity": "common", "weight": 0.1, "durability": 100, "addedIn": "2025", "tags": ["nutrition", "protein", "energy"], "hungerRestore": 25, "proteinContent": 20, "image": "assets/items/protein_bar.png"}, {"id": "water_purification_tablets", "name": "Water Purification Tablets", "code": "Water_Purification_Tablets", "description": "Chemical tablets for purifying contaminated water sources.", "category": "medical", "subcategory": "preventive", "rarity": "uncommon", "weight": 0.05, "durability": 100, "addedIn": "2025", "tags": ["water", "purification", "survival"], "uses": 20, "purificationRate": 95, "image": "assets/items/purification_tablets.png"}, {"id": "cigarettes", "name": "Cigarettes", "code": "Cigarettes", "description": "Pack of cigarettes that temporarily reduce stress but affect health.", "category": "misc", "subcategory": "consumables", "rarity": "common", "weight": 0.02, "durability": 100, "addedIn": "2024", "tags": ["stress", "addiction", "social"], "stressReduction": 15, "healthImpact": -5, "image": "assets/items/cigarettes.png"}, {"id": "hunting_rifle", "name": "Hunting Rifle", "code": "Hunting_Rifle", "description": "Bolt-action rifle designed for hunting large game with high accuracy.", "category": "weapons", "subcategory": "sniper_rifles", "rarity": "uncommon", "weight": 4.2, "durability": 120, "addedIn": "2024", "tags": ["bolt_action", "hunting", "precision"], "ammoType": ".308 Winchester", "damage": 75, "range": 800, "image": "assets/items/hunting_rifle.png"}, {"id": "shotgun_shell", "name": "12 Gauge Shotgun Shell", "code": "Shotgun_Shell_12ga", "description": "Standard 12-gauge shotgun ammunition for close-range combat.", "category": "ammo", "subcategory": "shotgun_ammo", "rarity": "common", "weight": 0.035, "durability": 100, "addedIn": "2024", "tags": ["shotgun", "close_range", "spread"], "caliber": "12 Gauge", "damage": 80, "spread": 15, "image": "assets/items/shotgun_shell.png"}, {"id": "combat_boots", "name": "Combat Boots", "code": "Combat_Boots", "description": "Durable military footwear providing protection and comfort.", "category": "armor", "subcategory": "footwear", "rarity": "common", "weight": 1.0, "durability": 100, "addedIn": "2024", "tags": ["footwear", "military", "protection"], "armorValue": 10, "movementBonus": 5, "image": "assets/items/combat_boots.png"}, {"id": "ghillie_suit", "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>_<PERSON>", "description": "Advanced camouflage suit providing excellent concealment in natural environments.", "category": "armor", "subcategory": "special_armor", "rarity": "epic", "weight": 2.5, "durability": 80, "addedIn": "2025", "tags": ["camouflage", "stealth", "special"], "camouflageValue": 85, "coverage": ["full_body"], "image": "assets/items/ghillie_suit.png"}, {"id": "gas_mask", "name": "Gas Mask", "code": "Gas_Mask", "description": "Protective mask designed to filter toxic gases and airborne contaminants.", "category": "armor", "subcategory": "protection", "rarity": "rare", "weight": 0.8, "durability": 60, "addedIn": "2025", "tags": ["protection", "gas", "toxic"], "filterDuration": 120, "protectionLevel": 90, "image": "assets/items/gas_mask.png"}, {"id": "bear_trap", "name": "Bear Trap", "code": "Bear_Trap", "description": "Heavy-duty mechanical trap designed to catch large animals.", "category": "tools", "subcategory": "traps", "rarity": "uncommon", "weight": 5.0, "durability": 150, "addedIn": "2024", "tags": ["trap", "hunting", "mechanical"], "damage": 50, "triggerSensitivity": 75, "image": "assets/items/bear_trap.png"}], "categories": {"weapons": {"name": "Weapons", "description": "Combat weapons including firearms, melee weapons, and explosives", "subcategories": {"assault_rifles": "Automatic rifles for medium to long range combat", "sniper_rifles": "Precision rifles for long-range engagements", "shotguns": "Close-range firearms with spread damage", "pistols": "Sidearms for backup and close quarters", "melee": "Hand-to-hand combat weapons", "explosives": "Grenades, mines, and other explosive devices"}}, "armor": {"name": "Armor & Clothing", "description": "Protective gear and clothing items", "subcategories": {"chest_armor": "Body armor and vests", "helmets": "Head protection", "footwear": "Boots and shoes", "special_armor": "Specialized protective equipment", "protection": "Environmental protection gear"}}, "food": {"name": "Food & Consumables", "description": "Edible items and beverages for survival", "subcategories": {"canned_food": "Preserved food in cans", "fresh_food": "Perishable food items", "beverages": "Drinks and liquids", "nutrition": "Specialized nutrition products"}}, "medical": {"name": "Medical Supplies", "description": "Healthcare and medical treatment items", "subcategories": {"treatment": "First aid and treatment kits", "medication": "Pills, injections, and medicines", "preventive": "Preventive care items"}}, "tools": {"name": "Tools & Equipment", "description": "Utility tools and equipment for various tasks", "subcategories": {"cutting_tools": "Knives, axes, and cutting implements", "construction": "Building and construction tools", "traps": "Hunting and defensive traps"}}, "ammo": {"name": "Ammunition", "description": "Projectiles and ammunition for weapons", "subcategories": {"rifle_ammo": "Ammunition for rifles", "pistol_ammo": "Ammunition for pistols", "shotgun_ammo": "Ammunition for shotguns", "special_ammo": "Specialized ammunition types"}}, "electronics": {"name": "Electronics", "description": "Electronic devices and gadgets", "subcategories": {"navigation": "GPS and navigation devices", "communication": "Radios and communication equipment", "optics": "Night vision and optical devices", "weapon_attachments": "Electronic weapon modifications"}}, "containers": {"name": "Containers", "description": "Storage containers and bags", "subcategories": {"backpacks": "Portable storage containers", "storage": "Stationary storage solutions"}}, "crafting": {"name": "Crafting Materials", "description": "Materials used for crafting and repairs", "subcategories": {"materials": "Raw materials for crafting"}}, "misc": {"name": "Miscellaneous", "description": "Various items that don't fit other categories", "subcategories": {"consumables": "Miscellaneous consumable items", "collectibles": "Rare and collectible items"}}}, "rarity": {"common": {"name": "Common", "color": "#6b7280", "description": "Easily found items that spawn frequently"}, "uncommon": {"name": "Uncommon", "color": "#10b981", "description": "Moderately rare items with decent spawn rates"}, "rare": {"name": "Rare", "color": "#3b82f6", "description": "Hard to find items with limited spawn locations"}, "epic": {"name": "Epic", "color": "#8b5cf6", "description": "Very rare items found in special locations"}, "legendary": {"name": "Legendary", "color": "#f59e0b", "description": "Extremely rare items with unique properties"}}}