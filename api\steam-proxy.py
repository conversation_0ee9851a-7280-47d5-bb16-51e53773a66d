#!/usr/bin/env python3
"""
Steam API Proxy for SCUM Commands 2025
Handles Steam Web API calls to avoid CORS issues
"""

import json
import time
import requests
from datetime import datetime, timedelta
from urllib.parse import urlencode
from flask import Flask, jsonify, request, Response
from flask_cors import CORS
import os
import sqlite3
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
STEAM_API_BASE = 'https://api.steampowered.com'
STEAM_STORE_BASE = 'https://store.steampowered.com'
SCUM_APP_ID = '513710'
CACHE_DURATION = 300  # 5 minutes

# Rate limiting
RATE_LIMIT_WINDOW = 60  # 1 minute
RATE_LIMIT_REQUESTS = 60  # Max requests per window
request_timestamps = []

class SteamAPIError(Exception):
    """Custom exception for Steam API errors"""
    pass

class RateLimitExceeded(Exception):
    """Exception for rate limit exceeded"""
    pass

def init_cache_db():
    """Initialize SQLite cache database"""
    conn = sqlite3.connect('steam_cache.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS steam_cache (
            key TEXT PRIMARY KEY,
            data TEXT NOT NULL,
            timestamp REAL NOT NULL,
            expires_at REAL NOT NULL
        )
    ''')
    
    # Clean up expired entries
    cursor.execute('DELETE FROM steam_cache WHERE expires_at < ?', (time.time(),))
    conn.commit()
    conn.close()

def get_cached_data(key):
    """Get cached data if still valid"""
    conn = sqlite3.connect('steam_cache.db')
    cursor = conn.cursor()
    cursor.execute(
        'SELECT data FROM steam_cache WHERE key = ? AND expires_at > ?',
        (key, time.time())
    )
    result = cursor.fetchone()
    conn.close()
    return json.loads(result[0]) if result else None

def cache_data(key, data, duration=CACHE_DURATION):
    """Cache data with expiration"""
    conn = sqlite3.connect('steam_cache.db')
    cursor = conn.cursor()
    expires_at = time.time() + duration
    cursor.execute(
        'INSERT OR REPLACE INTO steam_cache (key, data, timestamp, expires_at) VALUES (?, ?, ?, ?)',
        (key, json.dumps(data), time.time(), expires_at)
    )
    conn.commit()
    conn.close()

def check_rate_limit():
    """Check if request is within rate limits"""
    global request_timestamps
    now = time.time()
    
    # Remove old timestamps
    request_timestamps = [ts for ts in request_timestamps if now - ts < RATE_LIMIT_WINDOW]
    
    if len(request_timestamps) >= RATE_LIMIT_REQUESTS:
        raise RateLimitExceeded("Rate limit exceeded")
    
    request_timestamps.append(now)

def make_steam_request(url, params=None):
    """Make request to Steam API with error handling"""
    try:
        check_rate_limit()
        
        if params:
            url += '?' + urlencode(params)
        
        logger.info(f"Making Steam API request: {url}")
        
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        return response.json()
    
    except requests.exceptions.Timeout:
        raise SteamAPIError("Steam API request timed out")
    except requests.exceptions.RequestException as e:
        raise SteamAPIError(f"Steam API request failed: {str(e)}")
    except json.JSONDecodeError:
        raise SteamAPIError("Invalid JSON response from Steam API")

@app.route('/api/steam/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'scum_app_id': SCUM_APP_ID
    })

@app.route('/api/steam/players', methods=['GET'])
def get_current_players():
    """Get current SCUM player count"""
    cache_key = 'current_players'
    cached = get_cached_data(cache_key)
    if cached:
        return jsonify(cached)
    
    try:
        url = f"{STEAM_API_BASE}/ISteamUserStats/GetNumberOfCurrentPlayers/v1/"
        params = {'appid': SCUM_APP_ID}
        
        data = make_steam_request(url, params)
        
        if 'response' in data and 'player_count' in data['response']:
            result = {
                'player_count': data['response']['player_count'],
                'timestamp': datetime.now().isoformat(),
                'cached': False
            }
            cache_data(cache_key, result)
            return jsonify(result)
        else:
            raise SteamAPIError("Invalid player count response")
            
    except (SteamAPIError, RateLimitExceeded) as e:
        logger.error(f"Error getting player count: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/news', methods=['GET'])
def get_game_news():
    """Get SCUM game news"""
    count = request.args.get('count', 5, type=int)
    cache_key = f'game_news_{count}'
    cached = get_cached_data(cache_key)
    if cached:
        return jsonify(cached)
    
    try:
        url = f"{STEAM_API_BASE}/ISteamNews/GetNewsForApp/v2/"
        params = {
            'appid': SCUM_APP_ID,
            'count': min(count, 20),  # Limit to max 20
            'maxlength': 300,
            'format': 'json'
        }
        
        data = make_steam_request(url, params)
        
        if 'appnews' in data and 'newsitems' in data['appnews']:
            result = {
                'news': data['appnews']['newsitems'],
                'timestamp': datetime.now().isoformat(),
                'cached': False
            }
            cache_data(cache_key, result, duration=600)  # Cache news for 10 minutes
            return jsonify(result)
        else:
            raise SteamAPIError("Invalid news response")
            
    except (SteamAPIError, RateLimitExceeded) as e:
        logger.error(f"Error getting game news: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/store', methods=['GET'])
def get_store_data():
    """Get SCUM store information"""
    cache_key = 'store_data'
    cached = get_cached_data(cache_key)
    if cached:
        return jsonify(cached)
    
    try:
        url = f"{STEAM_STORE_BASE}/api/appdetails"
        params = {'appids': SCUM_APP_ID}
        
        data = make_steam_request(url, params)
        
        if SCUM_APP_ID in data and data[SCUM_APP_ID]['success']:
            result = {
                'store_data': data[SCUM_APP_ID]['data'],
                'timestamp': datetime.now().isoformat(),
                'cached': False
            }
            cache_data(cache_key, result, duration=3600)  # Cache store data for 1 hour
            return jsonify(result)
        else:
            raise SteamAPIError("Invalid store data response")
            
    except (SteamAPIError, RateLimitExceeded) as e:
        logger.error(f"Error getting store data: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/reviews', methods=['GET'])
def get_reviews_summary():
    """Get SCUM reviews summary"""
    cache_key = 'reviews_summary'
    cached = get_cached_data(cache_key)
    if cached:
        return jsonify(cached)
    
    try:
        url = f"{STEAM_STORE_BASE}/appreviews/{SCUM_APP_ID}"
        params = {
            'json': 1,
            'num_per_page': 0  # Just get summary
        }
        
        data = make_steam_request(url, params)
        
        if 'query_summary' in data:
            result = {
                'reviews': data['query_summary'],
                'timestamp': datetime.now().isoformat(),
                'cached': False
            }
            cache_data(cache_key, result, duration=1800)  # Cache reviews for 30 minutes
            return jsonify(result)
        else:
            raise SteamAPIError("Invalid reviews response")
            
    except (SteamAPIError, RateLimitExceeded) as e:
        logger.error(f"Error getting reviews: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/achievements', methods=['GET'])
def get_achievement_stats():
    """Get global achievement statistics"""
    cache_key = 'achievement_stats'
    cached = get_cached_data(cache_key)
    if cached:
        return jsonify(cached)
    
    try:
        url = f"{STEAM_API_BASE}/ISteamUserStats/GetGlobalAchievementPercentagesForApp/v2/"
        params = {'gameid': SCUM_APP_ID}
        
        data = make_steam_request(url, params)
        
        if 'achievementpercentages' in data and 'achievements' in data['achievementpercentages']:
            result = {
                'achievements': data['achievementpercentages']['achievements'],
                'timestamp': datetime.now().isoformat(),
                'cached': False
            }
            cache_data(cache_key, result, duration=3600)  # Cache achievements for 1 hour
            return jsonify(result)
        else:
            raise SteamAPIError("Invalid achievements response")
            
    except (SteamAPIError, RateLimitExceeded) as e:
        logger.error(f"Error getting achievements: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/comprehensive', methods=['GET'])
def get_comprehensive_stats():
    """Get all SCUM stats in one request"""
    try:
        # Try to get all data, but don't fail if some endpoints fail
        results = {}
        
        # Get player count
        try:
            players_data = get_current_players()
            if players_data.status_code == 200:
                results['players'] = players_data.get_json()
        except Exception as e:
            logger.warning(f"Failed to get player count: {str(e)}")
            results['players'] = None
        
        # Get news
        try:
            news_data = get_game_news()
            if news_data.status_code == 200:
                results['news'] = news_data.get_json()
        except Exception as e:
            logger.warning(f"Failed to get news: {str(e)}")
            results['news'] = None
        
        # Get store data
        try:
            store_data = get_store_data()
            if store_data.status_code == 200:
                results['store'] = store_data.get_json()
        except Exception as e:
            logger.warning(f"Failed to get store data: {str(e)}")
            results['store'] = None
        
        # Get reviews
        try:
            reviews_data = get_reviews_summary()
            if reviews_data.status_code == 200:
                results['reviews'] = reviews_data.get_json()
        except Exception as e:
            logger.warning(f"Failed to get reviews: {str(e)}")
            results['reviews'] = None
        
        return jsonify({
            'data': results,
            'timestamp': datetime.now().isoformat(),
            'success_count': len([v for v in results.values() if v is not None])
        })
        
    except Exception as e:
        logger.error(f"Error getting comprehensive stats: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/steam/cache/stats', methods=['GET'])
def get_cache_stats():
    """Get cache statistics"""
    conn = sqlite3.connect('steam_cache.db')
    cursor = conn.cursor()
    
    # Get total cached items
    cursor.execute('SELECT COUNT(*) FROM steam_cache')
    total_items = cursor.fetchone()[0]
    
    # Get valid cached items
    cursor.execute('SELECT COUNT(*) FROM steam_cache WHERE expires_at > ?', (time.time(),))
    valid_items = cursor.fetchone()[0]
    
    # Get cache keys and their expiration times
    cursor.execute('SELECT key, expires_at FROM steam_cache WHERE expires_at > ?', (time.time(),))
    cache_items = cursor.fetchall()
    
    conn.close()
    
    return jsonify({
        'total_items': total_items,
        'valid_items': valid_items,
        'expired_items': total_items - valid_items,
        'cache_keys': [{'key': key, 'expires_in': int(expires_at - time.time())} for key, expires_at in cache_items],
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/steam/cache/clear', methods=['POST'])
def clear_cache():
    """Clear all cached data"""
    conn = sqlite3.connect('steam_cache.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM steam_cache')
    deleted_count = cursor.rowcount
    conn.commit()
    conn.close()
    
    return jsonify({
        'message': 'Cache cleared successfully',
        'deleted_items': deleted_count,
        'timestamp': datetime.now().isoformat()
    })

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Initialize cache database
    init_cache_db()
    
    # Get port from environment or default to 5001
    port = int(os.environ.get('PORT', 5001))
    
    logger.info(f"Starting Steam API Proxy on port {port}")
    logger.info(f"SCUM App ID: {SCUM_APP_ID}")
    
    app.run(host='0.0.0.0', port=port, debug=False) 